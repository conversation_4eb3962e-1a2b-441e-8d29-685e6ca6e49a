{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import csv\n", "\n", "import numpy as np\n", "import tensorflow as tf\n", "from sklearn.model_selection import train_test_split\n", "\n", "RANDOM_SEED = 42"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 各パス指定"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["dataset = 'model/point_history_classifier/point_history.csv'\n", "model_save_path = 'model/point_history_classifier/point_history_classifier.hdf5'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 分類数設定"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["NUM_CLASSES = 4"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 入力長"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["TIME_STEPS = 16\n", "DIMENSION = 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 学習データ読み込み"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["X_dataset = np.loadtxt(dataset, delimiter=',', dtype='float32', usecols=list(range(1, (TIME_STEPS * DIMENSION) + 1)))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["y_dataset = np.loadtxt(dataset, delimiter=',', dtype='int32', usecols=(0))"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["X_train, X_test, y_train, y_test = train_test_split(X_dataset, y_dataset, train_size=0.75, random_state=RANDOM_SEED)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# モデル構築"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["use_lstm = False\n", "model = None\n", "\n", "if use_lstm:\n", "    model = tf.keras.models.Sequential([\n", "        tf.keras.layers.InputLayer(input_shape=(TIME_STEPS * DIMENSION, )),\n", "        tf.keras.layers.Reshape((TIME_STEPS, DIMENSION), input_shape=(TIME_STEPS * DIMENSION, )), \n", "        tf.keras.layers.Dropout(0.2),\n", "        tf.keras.layers.LSTM(16, input_shape=[TIME_STEPS, DIMENSION]),\n", "        tf.keras.layers.Dropout(0.5),\n", "        tf.keras.layers.Dense(10, activation='relu'),\n", "        tf.keras.layers.Dense(NUM_CLASSES, activation='softmax')\n", "    ])\n", "else:\n", "    model = tf.keras.models.Sequential([\n", "        tf.keras.layers.InputLayer(input_shape=(TIME_STEPS * DIMENSION, )),\n", "        tf.keras.layers.Dropout(0.2),\n", "        tf.keras.layers.Dense(24, activation='relu'),\n", "        tf.keras.layers.Dropout(0.5),\n", "        tf.keras.layers.Dense(10, activation='relu'),\n", "        tf.keras.layers.Dense(NUM_CLASSES, activation='softmax')\n", "    ])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"sequential\"\n", "_________________________________________________________________\n", "Layer (type)                 Output Shape              Param #   \n", "=================================================================\n", "dropout (Dropout)            (None, 32)                0         \n", "_________________________________________________________________\n", "dense (<PERSON><PERSON>)                (None, 24)                792       \n", "_________________________________________________________________\n", "dropout_1 (Dropout)          (None, 24)                0         \n", "_________________________________________________________________\n", "dense_1 (<PERSON><PERSON>)              (None, 10)                250       \n", "_________________________________________________________________\n", "dense_2 (<PERSON><PERSON>)              (<PERSON>, 4)                 44        \n", "=================================================================\n", "Total params: 1,086\n", "Trainable params: 1,086\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["model.summary()  # tf.keras.utils.plot_model(model, show_shapes=True)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# モデルチェックポイントのコールバック\n", "cp_callback = tf.keras.callbacks.ModelCheckpoint(\n", "    model_save_path, verbose=1, save_weights_only=False)\n", "# 早期打ち切り用コールバック\n", "es_callback = tf.keras.callbacks.EarlyStopping(patience=20, verbose=1)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["# モデルコンパイル\n", "model.compile(\n", "    optimizer='adam',\n", "    loss='sparse_categorical_crossentropy',\n", "    metrics=['accuracy']\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# モデル訓練"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 1.3800 - accuracy: 0.4279\n", "Epoch 00001: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 1s 25ms/step - loss: 1.3800 - accuracy: 0.4270 - val_loss: 1.3584 - val_accuracy: 0.5219\n", "Epoch 2/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 1.3587 - accuracy: 0.5066\n", "Epoch 00002: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 1.3586 - accuracy: 0.5068 - val_loss: 1.3365 - val_accuracy: 0.5310\n", "Epoch 3/1000\n", "32/32 [==============================] - ETA: 0s - loss: 1.3348 - accuracy: 0.5050\n", "Epoch 00003: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 1.3348 - accuracy: 0.5050 - val_loss: 1.3091 - val_accuracy: 0.5249\n", "Epoch 4/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 1.3090 - accuracy: 0.5237\n", "Epoch 00004: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 1.3090 - accuracy: 0.5239 - val_loss: 1.2752 - val_accuracy: 0.5906\n", "Epoch 5/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 1.2776 - accuracy: 0.5560\n", "Epoch 00005: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 13ms/step - loss: 1.2768 - accuracy: 0.5554 - val_loss: 1.2332 - val_accuracy: 0.6178\n", "Epoch 6/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 1.2342 - accuracy: 0.5597\n", "Epoch 00006: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 1.2342 - accuracy: 0.5599 - val_loss: 1.1817 - val_accuracy: 0.6858\n", "Epoch 7/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 1.1957 - accuracy: 0.5721\n", "Epoch 00007: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 1.1932 - accuracy: 0.5780 - val_loss: 1.1206 - val_accuracy: 0.7122\n", "Epoch 8/1000\n", "29/32 [==========================>...] - ETA: 0s - loss: 1.1480 - accuracy: 0.5717\n", "Epoch 00008: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 1.1455 - accuracy: 0.5743 - val_loss: 1.0555 - val_accuracy: 0.7492\n", "Epoch 9/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 1.0875 - accuracy: 0.6001\n", "Epoch 00009: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 1.0879 - accuracy: 0.5997 - val_loss: 0.9879 - val_accuracy: 0.7847\n", "Epoch 10/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 1.0461 - accuracy: 0.6023\n", "Epoch 00010: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 1.0451 - accuracy: 0.6037 - val_loss: 0.9241 - val_accuracy: 0.7878\n", "Epoch 11/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.9968 - accuracy: 0.6238\n", "Epoch 00011: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.9958 - accuracy: 0.6256 - val_loss: 0.8632 - val_accuracy: 0.8180\n", "Epoch 12/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.9525 - accuracy: 0.6356\n", "Epoch 00012: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.9523 - accuracy: 0.6357 - val_loss: 0.8063 - val_accuracy: 0.8270\n", "Epoch 13/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.9175 - accuracy: 0.6560\n", "Epoch 00013: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.9174 - accuracy: 0.6558 - val_loss: 0.7573 - val_accuracy: 0.8527\n", "Epoch 14/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.8813 - accuracy: 0.6709\n", "Epoch 00014: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.8738 - accuracy: 0.6689 - val_loss: 0.7132 - val_accuracy: 0.8739\n", "Epoch 15/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.8613 - accuracy: 0.6659\n", "Epoch 00015: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.8577 - accuracy: 0.6692 - val_loss: 0.6770 - val_accuracy: 0.8897\n", "Epoch 16/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.8275 - accuracy: 0.6815\n", "Epoch 00016: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.8273 - accuracy: 0.6815 - val_loss: 0.6460 - val_accuracy: 0.9026\n", "Epoch 17/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 0.8088 - accuracy: 0.6958\n", "Epoch 00017: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.8066 - accuracy: 0.6966 - val_loss: 0.6152 - val_accuracy: 0.9063\n", "Epoch 18/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 0.7810 - accuracy: 0.6953\n", "Epoch 00018: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.7807 - accuracy: 0.6954 - val_loss: 0.5880 - val_accuracy: 0.9162\n", "Epoch 19/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.7743 - accuracy: 0.6991\n", "Epoch 00019: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.7695 - accuracy: 0.7029 - val_loss: 0.5688 - val_accuracy: 0.9230\n", "Epoch 20/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.7537 - accuracy: 0.7121\n", "Epoch 00020: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.7469 - accuracy: 0.7127 - val_loss: 0.5462 - val_accuracy: 0.9222\n", "Epoch 21/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.7440 - accuracy: 0.7175\n", "Epoch 00021: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 1s 31ms/step - loss: 0.7457 - accuracy: 0.7168 - val_loss: 0.5261 - val_accuracy: 0.9313\n", "Epoch 22/1000\n", "29/32 [==========================>...] - ETA: 0s - loss: 0.7107 - accuracy: 0.7322\n", "Epoch 00022: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.7106 - accuracy: 0.7329 - val_loss: 0.5073 - val_accuracy: 0.9313\n", "Epoch 23/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.7039 - accuracy: 0.7356\n", "Epoch 00023: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 9ms/step - loss: 0.7039 - accuracy: 0.7356 - val_loss: 0.4947 - val_accuracy: 0.9358\n", "Epoch 24/1000\n", "24/32 [=====================>........] - ETA: 0s - loss: 0.7048 - accuracy: 0.7279\n", "Epoch 00024: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.7119 - accuracy: 0.7251 - val_loss: 0.4782 - val_accuracy: 0.9350\n", "Epoch 25/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.6741 - accuracy: 0.7485\n", "Epoch 00025: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.6737 - accuracy: 0.7485 - val_loss: 0.4624 - val_accuracy: 0.9366\n", "Epoch 26/1000\n", "23/32 [====================>.........] - ETA: 0s - loss: 0.6698 - accuracy: 0.7480\n", "Epoch 00026: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 9ms/step - loss: 0.6700 - accuracy: 0.7472 - val_loss: 0.4484 - val_accuracy: 0.9388\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 27/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.6723 - accuracy: 0.74 - ETA: 0s - loss: 0.6671 - accuracy: 0.7444\n", "Epoch 00027: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.6640 - accuracy: 0.7447 - val_loss: 0.4387 - val_accuracy: 0.9418\n", "Epoch 28/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.6625 - accuracy: 0.7533\n", "Epoch 00028: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.6622 - accuracy: 0.7535 - val_loss: 0.4314 - val_accuracy: 0.9411\n", "Epoch 29/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 0.6503 - accuracy: 0.7568\n", "Epoch 00029: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.6505 - accuracy: 0.7570 - val_loss: 0.4200 - val_accuracy: 0.9449\n", "Epoch 30/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.6271 - accuracy: 0.7675\n", "Epoch 00030: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.6296 - accuracy: 0.7656 - val_loss: 0.4093 - val_accuracy: 0.9524\n", "Epoch 31/1000\n", "29/32 [==========================>...] - ETA: 0s - loss: 0.6360 - accuracy: 0.7586\n", "Epoch 00031: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.6362 - accuracy: 0.7593 - val_loss: 0.4019 - val_accuracy: 0.9479\n", "Epoch 32/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.6253 - accuracy: 0.7700\n", "Epoch 00032: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.6186 - accuracy: 0.7717 - val_loss: 0.3927 - val_accuracy: 0.9464\n", "Epoch 33/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.6255 - accuracy: 0.7613\n", "Epoch 00033: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.6217 - accuracy: 0.7623 - val_loss: 0.3868 - val_accuracy: 0.9547\n", "Epoch 34/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.6175 - accuracy: 0.7678\n", "Epoch 00034: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.6136 - accuracy: 0.7694 - val_loss: 0.3795 - val_accuracy: 0.9547\n", "Epoch 35/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.6162 - accuracy: 0.7598\n", "Epoch 00035: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.6162 - accuracy: 0.7598 - val_loss: 0.3737 - val_accuracy: 0.9554\n", "Epoch 36/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.6299 - accuracy: 0.7622\n", "Epoch 00036: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.6154 - accuracy: 0.7711 - val_loss: 0.3709 - val_accuracy: 0.9577\n", "Epoch 37/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.6147 - accuracy: 0.7734\n", "Epoch 00037: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.6124 - accuracy: 0.7759 - val_loss: 0.3627 - val_accuracy: 0.9517\n", "Epoch 38/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.5937 - accuracy: 0.7773\n", "Epoch 00038: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.6087 - accuracy: 0.7722 - val_loss: 0.3583 - val_accuracy: 0.9524\n", "Epoch 39/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5917 - accuracy: 0.7831\n", "Epoch 00039: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5975 - accuracy: 0.7782 - val_loss: 0.3579 - val_accuracy: 0.9547\n", "Epoch 40/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.5938 - accuracy: 0.7749\n", "Epoch 00040: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5941 - accuracy: 0.7734 - val_loss: 0.3521 - val_accuracy: 0.9569\n", "Epoch 41/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.5979 - accuracy: 0.7656\n", "Epoch 00041: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5977 - accuracy: 0.7671 - val_loss: 0.3482 - val_accuracy: 0.9547\n", "Epoch 42/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5712 - accuracy: 0.7884\n", "Epoch 00042: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5693 - accuracy: 0.7888 - val_loss: 0.3442 - val_accuracy: 0.9524\n", "Epoch 43/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.5849 - accuracy: 0.7867\n", "Epoch 00043: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.5878 - accuracy: 0.7840 - val_loss: 0.3410 - val_accuracy: 0.9577\n", "Epoch 44/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.5745 - accuracy: 0.7810\n", "Epoch 00044: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5745 - accuracy: 0.7810 - val_loss: 0.3365 - val_accuracy: 0.9585\n", "Epoch 45/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.5725 - accuracy: 0.7760\n", "Epoch 00045: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.5731 - accuracy: 0.7754 - val_loss: 0.3323 - val_accuracy: 0.9569\n", "Epoch 46/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 0.5730 - accuracy: 0.7781\n", "Epoch 00046: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.5699 - accuracy: 0.7800 - val_loss: 0.3304 - val_accuracy: 0.9554\n", "Epoch 47/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 0.5533 - accuracy: 0.7945\n", "Epoch 00047: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5552 - accuracy: 0.7933 - val_loss: 0.3244 - val_accuracy: 0.9585\n", "Epoch 48/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5754 - accuracy: 0.7809\n", "Epoch 00048: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5751 - accuracy: 0.7827 - val_loss: 0.3202 - val_accuracy: 0.9577\n", "Epoch 49/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5672 - accuracy: 0.7816\n", "Epoch 00049: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5630 - accuracy: 0.7842 - val_loss: 0.3181 - val_accuracy: 0.9585\n", "Epoch 50/1000\n", "29/32 [==========================>...] - ETA: 0s - loss: 0.5576 - accuracy: 0.7877\n", "Epoch 00050: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.5561 - accuracy: 0.7888 - val_loss: 0.3148 - val_accuracy: 0.9577\n", "Epoch 51/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.5451 - accuracy: 0.8011\n", "Epoch 00051: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5442 - accuracy: 0.8014 - val_loss: 0.3096 - val_accuracy: 0.9532\n", "Epoch 52/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5685 - accuracy: 0.7884\n", "Epoch 00052: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.5681 - accuracy: 0.7915 - val_loss: 0.3070 - val_accuracy: 0.9569\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 53/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5480 - accuracy: 0.7966\n", "Epoch 00053: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5476 - accuracy: 0.7983 - val_loss: 0.3045 - val_accuracy: 0.9577\n", "Epoch 54/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5580 - accuracy: 0.7922\n", "Epoch 00054: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5476 - accuracy: 0.7931 - val_loss: 0.3040 - val_accuracy: 0.9577\n", "Epoch 55/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.5455 - accuracy: 0.7933\n", "Epoch 00055: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.5455 - accuracy: 0.7933 - val_loss: 0.3011 - val_accuracy: 0.9554\n", "Epoch 56/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5667 - accuracy: 0.7822\n", "Epoch 00056: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5651 - accuracy: 0.7858 - val_loss: 0.2987 - val_accuracy: 0.9554\n", "Epoch 57/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5463 - accuracy: 0.7900\n", "Epoch 00057: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5576 - accuracy: 0.7878 - val_loss: 0.3000 - val_accuracy: 0.9592\n", "Epoch 58/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.5439 - accuracy: 0.7958\n", "Epoch 00058: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5439 - accuracy: 0.7958 - val_loss: 0.2951 - val_accuracy: 0.9592\n", "Epoch 59/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5378 - accuracy: 0.8012\n", "Epoch 00059: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5442 - accuracy: 0.7988 - val_loss: 0.2946 - val_accuracy: 0.9622\n", "Epoch 60/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5143 - accuracy: 0.8103\n", "Epoch 00060: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5228 - accuracy: 0.8074 - val_loss: 0.2902 - val_accuracy: 0.9600\n", "Epoch 61/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5534 - accuracy: 0.7866\n", "Epoch 00061: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5451 - accuracy: 0.7903 - val_loss: 0.2894 - val_accuracy: 0.9592\n", "Epoch 62/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.5149 - accuracy: 0.8061\n", "Epoch 00062: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.5206 - accuracy: 0.8064 - val_loss: 0.2887 - val_accuracy: 0.9600\n", "Epoch 63/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 0.5289 - accuracy: 0.7966\n", "Epoch 00063: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.5304 - accuracy: 0.7953 - val_loss: 0.2851 - val_accuracy: 0.9569\n", "Epoch 64/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.5416 - accuracy: 0.7953\n", "Epoch 00064: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5416 - accuracy: 0.7953 - val_loss: 0.2861 - val_accuracy: 0.9592\n", "Epoch 65/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5304 - accuracy: 0.7947\n", "Epoch 00065: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5350 - accuracy: 0.7966 - val_loss: 0.2838 - val_accuracy: 0.9585\n", "Epoch 66/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.5162 - accuracy: 0.8056\n", "Epoch 00066: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5162 - accuracy: 0.8056 - val_loss: 0.2845 - val_accuracy: 0.9600\n", "Epoch 67/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.5406 - accuracy: 0.7944\n", "Epoch 00067: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.5405 - accuracy: 0.7943 - val_loss: 0.2809 - val_accuracy: 0.9607\n", "Epoch 68/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5099 - accuracy: 0.8081\n", "Epoch 00068: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5115 - accuracy: 0.8069 - val_loss: 0.2798 - val_accuracy: 0.9600\n", "Epoch 69/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.5113 - accuracy: 0.8064\n", "Epoch 00069: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.5164 - accuracy: 0.8026 - val_loss: 0.2810 - val_accuracy: 0.9592\n", "Epoch 70/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5161 - accuracy: 0.8028\n", "Epoch 00070: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5149 - accuracy: 0.8029 - val_loss: 0.2806 - val_accuracy: 0.9600\n", "Epoch 71/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5159 - accuracy: 0.8138\n", "Epoch 00071: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5198 - accuracy: 0.8104 - val_loss: 0.2794 - val_accuracy: 0.9577\n", "Epoch 72/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4899 - accuracy: 0.8138\n", "Epoch 00072: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4985 - accuracy: 0.8129 - val_loss: 0.2775 - val_accuracy: 0.9592\n", "Epoch 73/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5159 - accuracy: 0.8109\n", "Epoch 00073: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5128 - accuracy: 0.8104 - val_loss: 0.2789 - val_accuracy: 0.9592\n", "Epoch 74/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.5224 - accuracy: 0.8077\n", "Epoch 00074: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.5225 - accuracy: 0.8077 - val_loss: 0.2753 - val_accuracy: 0.9600\n", "Epoch 75/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5109 - accuracy: 0.8147\n", "Epoch 00075: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5102 - accuracy: 0.8134 - val_loss: 0.2728 - val_accuracy: 0.9600\n", "Epoch 76/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.5054 - accuracy: 0.8116\n", "Epoch 00076: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.5093 - accuracy: 0.8079 - val_loss: 0.2720 - val_accuracy: 0.9600\n", "Epoch 77/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4998 - accuracy: 0.8141\n", "Epoch 00077: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5006 - accuracy: 0.8119 - val_loss: 0.2702 - val_accuracy: 0.9585\n", "Epoch 78/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5134 - accuracy: 0.8106\n", "Epoch 00078: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5041 - accuracy: 0.8155 - val_loss: 0.2721 - val_accuracy: 0.9600\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 79/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.5063 - accuracy: 0.8122\n", "Epoch 00079: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5016 - accuracy: 0.8099 - val_loss: 0.2682 - val_accuracy: 0.9600\n", "Epoch 80/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4965 - accuracy: 0.8166\n", "Epoch 00080: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4887 - accuracy: 0.8200 - val_loss: 0.2658 - val_accuracy: 0.9600\n", "Epoch 81/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4992 - accuracy: 0.8169\n", "Epoch 00081: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4975 - accuracy: 0.8150 - val_loss: 0.2640 - val_accuracy: 0.9569\n", "Epoch 82/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4821 - accuracy: 0.8247\n", "Epoch 00082: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4789 - accuracy: 0.8253 - val_loss: 0.2617 - val_accuracy: 0.9569\n", "Epoch 83/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4900 - accuracy: 0.8181\n", "Epoch 00083: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4862 - accuracy: 0.8200 - val_loss: 0.2601 - val_accuracy: 0.9569\n", "Epoch 84/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.5119 - accuracy: 0.8094\n", "Epoch 00084: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 9ms/step - loss: 0.5119 - accuracy: 0.8094 - val_loss: 0.2598 - val_accuracy: 0.9577\n", "Epoch 85/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4991 - accuracy: 0.8095\n", "Epoch 00085: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4953 - accuracy: 0.8142 - val_loss: 0.2621 - val_accuracy: 0.9592\n", "Epoch 86/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4927 - accuracy: 0.8175\n", "Epoch 00086: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4987 - accuracy: 0.8117 - val_loss: 0.2577 - val_accuracy: 0.9569\n", "Epoch 87/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.5016 - accuracy: 0.8131\n", "Epoch 00087: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.5003 - accuracy: 0.8114 - val_loss: 0.2605 - val_accuracy: 0.9569\n", "Epoch 88/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4815 - accuracy: 0.8188\n", "Epoch 00088: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4806 - accuracy: 0.8205 - val_loss: 0.2572 - val_accuracy: 0.9569\n", "Epoch 89/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4686 - accuracy: 0.8244\n", "Epoch 00089: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4705 - accuracy: 0.8230 - val_loss: 0.2551 - val_accuracy: 0.9577\n", "Epoch 90/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.5002 - accuracy: 0.8131\n", "Epoch 00090: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4959 - accuracy: 0.8162 - val_loss: 0.2547 - val_accuracy: 0.9569\n", "Epoch 91/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4894 - accuracy: 0.8159\n", "Epoch 00091: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4848 - accuracy: 0.8192 - val_loss: 0.2544 - val_accuracy: 0.9585\n", "Epoch 92/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4848 - accuracy: 0.8209\n", "Epoch 00092: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4804 - accuracy: 0.8245 - val_loss: 0.2515 - val_accuracy: 0.9577\n", "Epoch 93/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4998 - accuracy: 0.8116\n", "Epoch 00093: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4910 - accuracy: 0.8122 - val_loss: 0.2546 - val_accuracy: 0.9600\n", "Epoch 94/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4816 - accuracy: 0.8194\n", "Epoch 00094: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4801 - accuracy: 0.8200 - val_loss: 0.2512 - val_accuracy: 0.9607\n", "Epoch 95/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4954 - accuracy: 0.8239\n", "Epoch 00095: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4861 - accuracy: 0.8243 - val_loss: 0.2509 - val_accuracy: 0.9607\n", "Epoch 96/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.4762 - accuracy: 0.8186\n", "Epoch 00096: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4739 - accuracy: 0.8197 - val_loss: 0.2487 - val_accuracy: 0.9592\n", "Epoch 97/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4780 - accuracy: 0.8222\n", "Epoch 00097: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4723 - accuracy: 0.8235 - val_loss: 0.2464 - val_accuracy: 0.9585\n", "Epoch 98/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.4746 - accuracy: 0.8239\n", "Epoch 00098: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4751 - accuracy: 0.8258 - val_loss: 0.2460 - val_accuracy: 0.9600\n", "Epoch 99/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.4791 - accuracy: 0.8198\n", "Epoch 00099: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4841 - accuracy: 0.8165 - val_loss: 0.2466 - val_accuracy: 0.9600\n", "Epoch 100/1000\n", "23/32 [====================>.........] - ETA: 0s - loss: 0.4916 - accuracy: 0.8142\n", "Epoch 00100: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 9ms/step - loss: 0.4755 - accuracy: 0.8240 - val_loss: 0.2439 - val_accuracy: 0.9592\n", "Epoch 101/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4731 - accuracy: 0.8285\n", "Epoch 00101: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4731 - accuracy: 0.8285 - val_loss: 0.2438 - val_accuracy: 0.9585\n", "Epoch 102/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4815 - accuracy: 0.8200\n", "Epoch 00102: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4865 - accuracy: 0.8207 - val_loss: 0.2436 - val_accuracy: 0.9592\n", "Epoch 103/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4595 - accuracy: 0.8338\n", "Epoch 00103: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4699 - accuracy: 0.8293 - val_loss: 0.2447 - val_accuracy: 0.9577\n", "Epoch 104/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4708 - accuracy: 0.8284\n", "Epoch 00104: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4684 - accuracy: 0.8278 - val_loss: 0.2446 - val_accuracy: 0.9562\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 105/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4843 - accuracy: 0.8194\n", "Epoch 00105: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4775 - accuracy: 0.8207 - val_loss: 0.2425 - val_accuracy: 0.9569\n", "Epoch 106/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4649 - accuracy: 0.8291\n", "Epoch 00106: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4692 - accuracy: 0.8293 - val_loss: 0.2411 - val_accuracy: 0.9585\n", "Epoch 107/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.4895 - accuracy: 0.8244\n", "Epoch 00107: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4843 - accuracy: 0.8253 - val_loss: 0.2416 - val_accuracy: 0.9569\n", "Epoch 108/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.4617 - accuracy: 0.8294\n", "Epoch 00108: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.4617 - accuracy: 0.8293 - val_loss: 0.2388 - val_accuracy: 0.9569\n", "Epoch 109/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.4640 - accuracy: 0.8273\n", "Epoch 00109: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4666 - accuracy: 0.8253 - val_loss: 0.2393 - val_accuracy: 0.9569\n", "Epoch 110/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4651 - accuracy: 0.8335\n", "Epoch 00110: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4697 - accuracy: 0.8318 - val_loss: 0.2385 - val_accuracy: 0.9577\n", "Epoch 111/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4739 - accuracy: 0.8309\n", "Epoch 00111: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4792 - accuracy: 0.8248 - val_loss: 0.2364 - val_accuracy: 0.9577\n", "Epoch 112/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4624 - accuracy: 0.8248\n", "Epoch 00112: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4659 - accuracy: 0.8230 - val_loss: 0.2362 - val_accuracy: 0.9569\n", "Epoch 113/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4403 - accuracy: 0.8400\n", "Epoch 00113: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4432 - accuracy: 0.8386 - val_loss: 0.2324 - val_accuracy: 0.9577\n", "Epoch 114/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.4608 - accuracy: 0.8354\n", "Epoch 00114: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4623 - accuracy: 0.8353 - val_loss: 0.2309 - val_accuracy: 0.9607\n", "Epoch 115/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4641 - accuracy: 0.8359\n", "Epoch 00115: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4585 - accuracy: 0.8376 - val_loss: 0.2295 - val_accuracy: 0.9585\n", "Epoch 116/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.4690 - accuracy: 0.8319\n", "Epoch 00116: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4704 - accuracy: 0.8318 - val_loss: 0.2320 - val_accuracy: 0.9592\n", "Epoch 117/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4509 - accuracy: 0.8371\n", "Epoch 00117: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4509 - accuracy: 0.8371 - val_loss: 0.2293 - val_accuracy: 0.9585\n", "Epoch 118/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4597 - accuracy: 0.8323\n", "Epoch 00118: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4597 - accuracy: 0.8323 - val_loss: 0.2303 - val_accuracy: 0.9577\n", "Epoch 119/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4703 - accuracy: 0.8372\n", "Epoch 00119: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4568 - accuracy: 0.8424 - val_loss: 0.2282 - val_accuracy: 0.9569\n", "Epoch 120/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4411 - accuracy: 0.8400\n", "Epoch 00120: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4417 - accuracy: 0.8351 - val_loss: 0.2253 - val_accuracy: 0.9577\n", "Epoch 121/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.4730 - accuracy: 0.8220\n", "Epoch 00121: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4674 - accuracy: 0.8253 - val_loss: 0.2264 - val_accuracy: 0.9585\n", "Epoch 122/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4386 - accuracy: 0.8391\n", "Epoch 00122: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4471 - accuracy: 0.8364 - val_loss: 0.2233 - val_accuracy: 0.9585\n", "Epoch 123/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4318 - accuracy: 0.8409\n", "Epoch 00123: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4262 - accuracy: 0.8429 - val_loss: 0.2233 - val_accuracy: 0.9562\n", "Epoch 124/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4740 - accuracy: 0.8319\n", "Epoch 00124: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.4678 - accuracy: 0.8336 - val_loss: 0.2233 - val_accuracy: 0.9554\n", "Epoch 125/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.4577 - accuracy: 0.8267\n", "Epoch 00125: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4563 - accuracy: 0.8283 - val_loss: 0.2235 - val_accuracy: 0.9569\n", "Epoch 126/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4617 - accuracy: 0.8331\n", "Epoch 00126: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4529 - accuracy: 0.8336 - val_loss: 0.2229 - val_accuracy: 0.9569\n", "Epoch 127/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4534 - accuracy: 0.8392\n", "Epoch 00127: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4512 - accuracy: 0.8416 - val_loss: 0.2239 - val_accuracy: 0.9577\n", "Epoch 128/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4352 - accuracy: 0.8410\n", "Epoch 00128: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4357 - accuracy: 0.8411 - val_loss: 0.2234 - val_accuracy: 0.9577\n", "Epoch 129/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4300 - accuracy: 0.8425\n", "Epoch 00129: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4386 - accuracy: 0.8416 - val_loss: 0.2206 - val_accuracy: 0.9607\n", "Epoch 130/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4515 - accuracy: 0.8293\n", "Epoch 00130: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4515 - accuracy: 0.8293 - val_loss: 0.2227 - val_accuracy: 0.9577\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 131/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4412 - accuracy: 0.8319\n", "Epoch 00131: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4465 - accuracy: 0.8328 - val_loss: 0.2240 - val_accuracy: 0.9585\n", "Epoch 132/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4451 - accuracy: 0.8441\n", "Epoch 00132: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4543 - accuracy: 0.8381 - val_loss: 0.2201 - val_accuracy: 0.9600\n", "Epoch 133/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4400 - accuracy: 0.8350\n", "Epoch 00133: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4446 - accuracy: 0.8351 - val_loss: 0.2192 - val_accuracy: 0.9577\n", "Epoch 134/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4566 - accuracy: 0.8434\n", "Epoch 00134: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4601 - accuracy: 0.8386 - val_loss: 0.2203 - val_accuracy: 0.9585\n", "Epoch 135/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4525 - accuracy: 0.8303\n", "Epoch 00135: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4485 - accuracy: 0.8316 - val_loss: 0.2199 - val_accuracy: 0.9592\n", "Epoch 136/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4410 - accuracy: 0.8388\n", "Epoch 00136: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4423 - accuracy: 0.8356 - val_loss: 0.2172 - val_accuracy: 0.9592\n", "Epoch 137/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.4492 - accuracy: 0.8409\n", "Epoch 00137: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4459 - accuracy: 0.8414 - val_loss: 0.2194 - val_accuracy: 0.9585\n", "Epoch 138/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4431 - accuracy: 0.8413\n", "Epoch 00138: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4336 - accuracy: 0.8447 - val_loss: 0.2201 - val_accuracy: 0.9592\n", "Epoch 139/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4484 - accuracy: 0.8419\n", "Epoch 00139: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4490 - accuracy: 0.8444 - val_loss: 0.2159 - val_accuracy: 0.9615\n", "Epoch 140/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4594 - accuracy: 0.8302\n", "Epoch 00140: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4550 - accuracy: 0.8336 - val_loss: 0.2190 - val_accuracy: 0.9592\n", "Epoch 141/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4514 - accuracy: 0.8311\n", "Epoch 00141: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4343 - accuracy: 0.8366 - val_loss: 0.2177 - val_accuracy: 0.9615\n", "Epoch 142/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.4508 - accuracy: 0.8414\n", "Epoch 00142: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4491 - accuracy: 0.8391 - val_loss: 0.2159 - val_accuracy: 0.9622\n", "Epoch 143/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4407 - accuracy: 0.8394\n", "Epoch 00143: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.4407 - accuracy: 0.8394 - val_loss: 0.2144 - val_accuracy: 0.9607\n", "Epoch 144/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4235 - accuracy: 0.8409\n", "Epoch 00144: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.4235 - accuracy: 0.8409 - val_loss: 0.2124 - val_accuracy: 0.9607\n", "Epoch 145/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4487 - accuracy: 0.8388\n", "Epoch 00145: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4463 - accuracy: 0.8391 - val_loss: 0.2112 - val_accuracy: 0.9622\n", "Epoch 146/1000\n", "29/32 [==========================>...] - ETA: 0s - loss: 0.4367 - accuracy: 0.8411\n", "Epoch 00146: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4410 - accuracy: 0.8394 - val_loss: 0.2126 - val_accuracy: 0.9615\n", "Epoch 147/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.4365 - accuracy: 0.8409\n", "Epoch 00147: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.4375 - accuracy: 0.8396 - val_loss: 0.2132 - val_accuracy: 0.9622\n", "Epoch 148/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.4563 - accuracy: 0.8417\n", "Epoch 00148: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4547 - accuracy: 0.8406 - val_loss: 0.2151 - val_accuracy: 0.9615\n", "Epoch 149/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.4672 - accuracy: 0.8306\n", "Epoch 00149: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4669 - accuracy: 0.8308 - val_loss: 0.2131 - val_accuracy: 0.9577\n", "Epoch 150/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4482 - accuracy: 0.8469\n", "Epoch 00150: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4396 - accuracy: 0.8447 - val_loss: 0.2116 - val_accuracy: 0.9585\n", "Epoch 151/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4280 - accuracy: 0.8394\n", "Epoch 00151: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4341 - accuracy: 0.8371 - val_loss: 0.2113 - val_accuracy: 0.9637\n", "Epoch 152/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.4177 - accuracy: 0.8463\n", "Epoch 00152: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4176 - accuracy: 0.8459 - val_loss: 0.2090 - val_accuracy: 0.9607\n", "Epoch 153/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4303 - accuracy: 0.8438\n", "Epoch 00153: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4330 - accuracy: 0.8429 - val_loss: 0.2087 - val_accuracy: 0.9600\n", "Epoch 154/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4360 - accuracy: 0.8375\n", "Epoch 00154: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4398 - accuracy: 0.8384 - val_loss: 0.2086 - val_accuracy: 0.9622\n", "Epoch 155/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4393 - accuracy: 0.8413\n", "Epoch 00155: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4368 - accuracy: 0.8419 - val_loss: 0.2112 - val_accuracy: 0.9615\n", "Epoch 156/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4296 - accuracy: 0.8428\n", "Epoch 00156: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4419 - accuracy: 0.8346 - val_loss: 0.2085 - val_accuracy: 0.9615\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 157/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4322 - accuracy: 0.8384\n", "Epoch 00157: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4319 - accuracy: 0.8404 - val_loss: 0.2081 - val_accuracy: 0.9615\n", "Epoch 158/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4179 - accuracy: 0.8522\n", "Epoch 00158: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4218 - accuracy: 0.8482 - val_loss: 0.2074 - val_accuracy: 0.9622\n", "Epoch 159/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4368 - accuracy: 0.8450\n", "Epoch 00159: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4420 - accuracy: 0.8426 - val_loss: 0.2090 - val_accuracy: 0.9637\n", "Epoch 160/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4331 - accuracy: 0.8431\n", "Epoch 00160: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4306 - accuracy: 0.8469 - val_loss: 0.2087 - val_accuracy: 0.9645\n", "Epoch 161/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4576 - accuracy: 0.8400\n", "Epoch 00161: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4504 - accuracy: 0.8404 - val_loss: 0.2059 - val_accuracy: 0.9645\n", "Epoch 162/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4418 - accuracy: 0.8419\n", "Epoch 00162: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4515 - accuracy: 0.8401 - val_loss: 0.2073 - val_accuracy: 0.9645\n", "Epoch 163/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4191 - accuracy: 0.8444\n", "Epoch 00163: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4207 - accuracy: 0.8477 - val_loss: 0.2066 - val_accuracy: 0.9615\n", "Epoch 164/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4432 - accuracy: 0.8450\n", "Epoch 00164: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4362 - accuracy: 0.8469 - val_loss: 0.2086 - val_accuracy: 0.9630\n", "Epoch 165/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4218 - accuracy: 0.8453\n", "Epoch 00165: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4329 - accuracy: 0.8421 - val_loss: 0.2062 - val_accuracy: 0.9637\n", "Epoch 166/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4230 - accuracy: 0.8481\n", "Epoch 00166: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4261 - accuracy: 0.8459 - val_loss: 0.2097 - val_accuracy: 0.9637\n", "Epoch 167/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4407 - accuracy: 0.8388\n", "Epoch 00167: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4283 - accuracy: 0.8416 - val_loss: 0.2070 - val_accuracy: 0.9645\n", "Epoch 168/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4286 - accuracy: 0.8407\n", "Epoch 00168: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4270 - accuracy: 0.8411 - val_loss: 0.2038 - val_accuracy: 0.9653\n", "Epoch 169/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4126 - accuracy: 0.8450\n", "Epoch 00169: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4118 - accuracy: 0.8457 - val_loss: 0.2016 - val_accuracy: 0.9630\n", "Epoch 170/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4269 - accuracy: 0.8375\n", "Epoch 00170: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4304 - accuracy: 0.8364 - val_loss: 0.2017 - val_accuracy: 0.9653\n", "Epoch 171/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4282 - accuracy: 0.8419\n", "Epoch 00171: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4291 - accuracy: 0.8406 - val_loss: 0.2011 - val_accuracy: 0.9660\n", "Epoch 172/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.4177 - accuracy: 0.8453\n", "Epoch 00172: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4199 - accuracy: 0.8449 - val_loss: 0.1993 - val_accuracy: 0.9653\n", "Epoch 173/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3980 - accuracy: 0.8492\n", "Epoch 00173: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 9ms/step - loss: 0.4059 - accuracy: 0.8439 - val_loss: 0.1997 - val_accuracy: 0.9653\n", "Epoch 174/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4266 - accuracy: 0.8395\n", "Epoch 00174: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4194 - accuracy: 0.8429 - val_loss: 0.1983 - val_accuracy: 0.9660\n", "Epoch 175/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4328 - accuracy: 0.8369\n", "Epoch 00175: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 9ms/step - loss: 0.4328 - accuracy: 0.8369 - val_loss: 0.2009 - val_accuracy: 0.9660\n", "Epoch 176/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4045 - accuracy: 0.8562\n", "Epoch 00176: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.4045 - accuracy: 0.8562 - val_loss: 0.1980 - val_accuracy: 0.9645\n", "Epoch 177/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3965 - accuracy: 0.8569\n", "Epoch 00177: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4061 - accuracy: 0.8507 - val_loss: 0.1951 - val_accuracy: 0.9653\n", "Epoch 178/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4231 - accuracy: 0.8468\n", "Epoch 00178: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4147 - accuracy: 0.8499 - val_loss: 0.1957 - val_accuracy: 0.9660\n", "Epoch 179/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.4098 - accuracy: 0.8487\n", "Epoch 00179: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4150 - accuracy: 0.8479 - val_loss: 0.1940 - val_accuracy: 0.9675\n", "Epoch 180/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3973 - accuracy: 0.8561\n", "Epoch 00180: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3999 - accuracy: 0.8573 - val_loss: 0.1942 - val_accuracy: 0.9668\n", "Epoch 181/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4120 - accuracy: 0.8540\n", "Epoch 00181: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4055 - accuracy: 0.8562 - val_loss: 0.1932 - val_accuracy: 0.9668\n", "Epoch 182/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4321 - accuracy: 0.8457\n", "Epoch 00182: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4321 - accuracy: 0.8457 - val_loss: 0.1927 - val_accuracy: 0.9660\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 183/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4351 - accuracy: 0.8459\n", "Epoch 00183: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4335 - accuracy: 0.8482 - val_loss: 0.1944 - val_accuracy: 0.9645\n", "Epoch 184/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4186 - accuracy: 0.8422\n", "Epoch 00184: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4222 - accuracy: 0.8454 - val_loss: 0.1945 - val_accuracy: 0.9645\n", "Epoch 185/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4106 - accuracy: 0.8459\n", "Epoch 00185: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4106 - accuracy: 0.8459 - val_loss: 0.1939 - val_accuracy: 0.9660\n", "Epoch 186/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4146 - accuracy: 0.8478\n", "Epoch 00186: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4052 - accuracy: 0.8502 - val_loss: 0.1958 - val_accuracy: 0.9653\n", "Epoch 187/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4220 - accuracy: 0.8566\n", "Epoch 00187: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4220 - accuracy: 0.8575 - val_loss: 0.1955 - val_accuracy: 0.9660\n", "Epoch 188/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4061 - accuracy: 0.8562\n", "Epoch 00188: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4066 - accuracy: 0.8552 - val_loss: 0.1928 - val_accuracy: 0.9645\n", "Epoch 189/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4161 - accuracy: 0.8486\n", "Epoch 00189: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4174 - accuracy: 0.8459 - val_loss: 0.1945 - val_accuracy: 0.9653\n", "Epoch 190/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3969 - accuracy: 0.8597\n", "Epoch 00190: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4110 - accuracy: 0.8580 - val_loss: 0.1907 - val_accuracy: 0.9645\n", "Epoch 191/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4214 - accuracy: 0.8432\n", "Epoch 00191: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4214 - accuracy: 0.8432 - val_loss: 0.1925 - val_accuracy: 0.9630\n", "Epoch 192/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4095 - accuracy: 0.8471\n", "Epoch 00192: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4095 - accuracy: 0.8464 - val_loss: 0.1931 - val_accuracy: 0.9637\n", "Epoch 193/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4241 - accuracy: 0.8492\n", "Epoch 00193: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4146 - accuracy: 0.8510 - val_loss: 0.1926 - val_accuracy: 0.9645\n", "Epoch 194/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4242 - accuracy: 0.8520\n", "Epoch 00194: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4242 - accuracy: 0.8520 - val_loss: 0.1934 - val_accuracy: 0.9630\n", "Epoch 195/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4063 - accuracy: 0.8505\n", "Epoch 00195: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4063 - accuracy: 0.8505 - val_loss: 0.1918 - val_accuracy: 0.9637\n", "Epoch 196/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4147 - accuracy: 0.8438\n", "Epoch 00196: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4158 - accuracy: 0.8419 - val_loss: 0.1922 - val_accuracy: 0.9653\n", "Epoch 197/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4135 - accuracy: 0.8475\n", "Epoch 00197: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4124 - accuracy: 0.8502 - val_loss: 0.1919 - val_accuracy: 0.9660\n", "Epoch 198/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4024 - accuracy: 0.8556\n", "Epoch 00198: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4041 - accuracy: 0.8525 - val_loss: 0.1923 - val_accuracy: 0.9653\n", "Epoch 199/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4030 - accuracy: 0.8562\n", "Epoch 00199: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4089 - accuracy: 0.8560 - val_loss: 0.1907 - val_accuracy: 0.9660\n", "Epoch 200/1000\n", "29/32 [==========================>...] - ETA: 0s - loss: 0.4057 - accuracy: 0.8491\n", "Epoch 00200: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4102 - accuracy: 0.8474 - val_loss: 0.1917 - val_accuracy: 0.9653\n", "Epoch 201/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4163 - accuracy: 0.8559\n", "Epoch 00201: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4107 - accuracy: 0.8573 - val_loss: 0.1913 - val_accuracy: 0.9660\n", "Epoch 202/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4054 - accuracy: 0.8503\n", "Epoch 00202: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4091 - accuracy: 0.8482 - val_loss: 0.1881 - val_accuracy: 0.9668\n", "Epoch 203/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4213 - accuracy: 0.8512\n", "Epoch 00203: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4200 - accuracy: 0.8527 - val_loss: 0.1891 - val_accuracy: 0.9668\n", "Epoch 204/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4040 - accuracy: 0.8544\n", "Epoch 00204: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3927 - accuracy: 0.8588 - val_loss: 0.1875 - val_accuracy: 0.9668\n", "Epoch 205/1000\n", "29/32 [==========================>...] - ETA: 0s - loss: 0.4046 - accuracy: 0.8454\n", "Epoch 00205: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4003 - accuracy: 0.8469 - val_loss: 0.1835 - val_accuracy: 0.9660\n", "Epoch 206/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3951 - accuracy: 0.8491\n", "Epoch 00206: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4090 - accuracy: 0.8447 - val_loss: 0.1851 - val_accuracy: 0.9668\n", "Epoch 207/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4145 - accuracy: 0.8469\n", "Epoch 00207: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4180 - accuracy: 0.8439 - val_loss: 0.1863 - val_accuracy: 0.9675\n", "Epoch 208/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4117 - accuracy: 0.8517\n", "Epoch 00208: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4117 - accuracy: 0.8517 - val_loss: 0.1864 - val_accuracy: 0.9645\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 209/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4140 - accuracy: 0.8407\n", "Epoch 00209: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4080 - accuracy: 0.8444 - val_loss: 0.1874 - val_accuracy: 0.9645\n", "Epoch 210/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.4091 - accuracy: 0.8567\n", "Epoch 00210: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4091 - accuracy: 0.8567 - val_loss: 0.1879 - val_accuracy: 0.9683\n", "Epoch 211/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.4135 - accuracy: 0.8418\n", "Epoch 00211: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4142 - accuracy: 0.8429 - val_loss: 0.1873 - val_accuracy: 0.9675\n", "Epoch 212/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3887 - accuracy: 0.8600\n", "Epoch 00212: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3889 - accuracy: 0.8580 - val_loss: 0.1868 - val_accuracy: 0.9645\n", "Epoch 213/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3821 - accuracy: 0.8537\n", "Epoch 00213: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3945 - accuracy: 0.8494 - val_loss: 0.1874 - val_accuracy: 0.9645\n", "Epoch 214/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3953 - accuracy: 0.8531\n", "Epoch 00214: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3957 - accuracy: 0.8535 - val_loss: 0.1881 - val_accuracy: 0.9645\n", "Epoch 215/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3830 - accuracy: 0.8603\n", "Epoch 00215: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3836 - accuracy: 0.8630 - val_loss: 0.1843 - val_accuracy: 0.9668\n", "Epoch 216/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3839 - accuracy: 0.8588\n", "Epoch 00216: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3831 - accuracy: 0.8623 - val_loss: 0.1822 - val_accuracy: 0.9668\n", "Epoch 217/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3974 - accuracy: 0.8509\n", "Epoch 00217: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3966 - accuracy: 0.8525 - val_loss: 0.1835 - val_accuracy: 0.9668\n", "Epoch 218/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3907 - accuracy: 0.8572\n", "Epoch 00218: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3962 - accuracy: 0.8550 - val_loss: 0.1864 - val_accuracy: 0.9690\n", "Epoch 219/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3961 - accuracy: 0.8597\n", "Epoch 00219: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3976 - accuracy: 0.8575 - val_loss: 0.1851 - val_accuracy: 0.9668\n", "Epoch 220/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.4082 - accuracy: 0.8530\n", "Epoch 00220: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4082 - accuracy: 0.8520 - val_loss: 0.1877 - val_accuracy: 0.9653\n", "Epoch 221/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3963 - accuracy: 0.8588\n", "Epoch 00221: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3989 - accuracy: 0.8562 - val_loss: 0.1850 - val_accuracy: 0.9675\n", "Epoch 222/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3959 - accuracy: 0.8491\n", "Epoch 00222: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4026 - accuracy: 0.8464 - val_loss: 0.1819 - val_accuracy: 0.9675\n", "Epoch 223/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3829 - accuracy: 0.8576\n", "Epoch 00223: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3892 - accuracy: 0.8557 - val_loss: 0.1818 - val_accuracy: 0.9683\n", "Epoch 224/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4184 - accuracy: 0.8491\n", "Epoch 00224: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4080 - accuracy: 0.8537 - val_loss: 0.1834 - val_accuracy: 0.9675\n", "Epoch 225/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3915 - accuracy: 0.8575\n", "Epoch 00225: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3959 - accuracy: 0.8567 - val_loss: 0.1817 - val_accuracy: 0.9683\n", "Epoch 226/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3692 - accuracy: 0.8616\n", "Epoch 00226: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3785 - accuracy: 0.8603 - val_loss: 0.1795 - val_accuracy: 0.9660\n", "Epoch 227/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.3888 - accuracy: 0.8646\n", "Epoch 00227: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3913 - accuracy: 0.8628 - val_loss: 0.1802 - val_accuracy: 0.9698\n", "Epoch 228/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3877 - accuracy: 0.8622\n", "Epoch 00228: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3884 - accuracy: 0.8630 - val_loss: 0.1800 - val_accuracy: 0.9690\n", "Epoch 229/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.3978 - accuracy: 0.8568\n", "Epoch 00229: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3927 - accuracy: 0.8565 - val_loss: 0.1780 - val_accuracy: 0.9698\n", "Epoch 230/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4260 - accuracy: 0.8434\n", "Epoch 00230: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4161 - accuracy: 0.8487 - val_loss: 0.1788 - val_accuracy: 0.9690\n", "Epoch 231/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4188 - accuracy: 0.8486\n", "Epoch 00231: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4204 - accuracy: 0.8484 - val_loss: 0.1831 - val_accuracy: 0.9683\n", "Epoch 232/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3895 - accuracy: 0.8556\n", "Epoch 00232: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4008 - accuracy: 0.8512 - val_loss: 0.1822 - val_accuracy: 0.9690\n", "Epoch 233/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3864 - accuracy: 0.8555\n", "Epoch 00233: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3864 - accuracy: 0.8555 - val_loss: 0.1806 - val_accuracy: 0.9683\n", "Epoch 234/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3975 - accuracy: 0.8547\n", "Epoch 00234: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3973 - accuracy: 0.8555 - val_loss: 0.1793 - val_accuracy: 0.9675\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 235/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3989 - accuracy: 0.8610\n", "Epoch 00235: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3989 - accuracy: 0.8610 - val_loss: 0.1768 - val_accuracy: 0.9705\n", "Epoch 236/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.4107 - accuracy: 0.8539\n", "Epoch 00236: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.4055 - accuracy: 0.8540 - val_loss: 0.1794 - val_accuracy: 0.9668\n", "Epoch 237/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3972 - accuracy: 0.8484\n", "Epoch 00237: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4037 - accuracy: 0.8474 - val_loss: 0.1788 - val_accuracy: 0.9683\n", "Epoch 238/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4146 - accuracy: 0.8434\n", "Epoch 00238: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4144 - accuracy: 0.8469 - val_loss: 0.1790 - val_accuracy: 0.9690\n", "Epoch 239/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.3965 - accuracy: 0.8555\n", "Epoch 00239: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3964 - accuracy: 0.8542 - val_loss: 0.1792 - val_accuracy: 0.9698\n", "Epoch 240/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3815 - accuracy: 0.8672\n", "Epoch 00240: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3920 - accuracy: 0.8610 - val_loss: 0.1770 - val_accuracy: 0.9683\n", "Epoch 241/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.3868 - accuracy: 0.8599\n", "Epoch 00241: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3964 - accuracy: 0.8573 - val_loss: 0.1806 - val_accuracy: 0.9690\n", "Epoch 242/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.3872 - accuracy: 0.8620\n", "Epoch 00242: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3890 - accuracy: 0.8595 - val_loss: 0.1780 - val_accuracy: 0.9690\n", "Epoch 243/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4022 - accuracy: 0.8522\n", "Epoch 00243: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3918 - accuracy: 0.8583 - val_loss: 0.1765 - val_accuracy: 0.9713\n", "Epoch 244/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 0.3980 - accuracy: 0.8536\n", "Epoch 00244: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 9ms/step - loss: 0.3987 - accuracy: 0.8520 - val_loss: 0.1775 - val_accuracy: 0.9713\n", "Epoch 245/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3966 - accuracy: 0.8546\n", "Epoch 00245: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3903 - accuracy: 0.8550 - val_loss: 0.1756 - val_accuracy: 0.9713\n", "Epoch 246/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.3880 - accuracy: 0.8686\n", "Epoch 00246: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3901 - accuracy: 0.8648 - val_loss: 0.1742 - val_accuracy: 0.9690\n", "Epoch 247/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3851 - accuracy: 0.8569\n", "Epoch 00247: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3908 - accuracy: 0.8557 - val_loss: 0.1742 - val_accuracy: 0.9675\n", "Epoch 248/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4120 - accuracy: 0.8512\n", "Epoch 00248: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4064 - accuracy: 0.8530 - val_loss: 0.1757 - val_accuracy: 0.9690\n", "Epoch 249/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.3972 - accuracy: 0.8585\n", "Epoch 00249: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3918 - accuracy: 0.8605 - val_loss: 0.1774 - val_accuracy: 0.9668\n", "Epoch 250/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.3972 - accuracy: 0.8530\n", "Epoch 00250: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3940 - accuracy: 0.8547 - val_loss: 0.1753 - val_accuracy: 0.9690\n", "Epoch 251/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3748 - accuracy: 0.8619\n", "Epoch 00251: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3883 - accuracy: 0.8598 - val_loss: 0.1729 - val_accuracy: 0.9690\n", "Epoch 252/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4063 - accuracy: 0.8534\n", "Epoch 00252: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4004 - accuracy: 0.8545 - val_loss: 0.1749 - val_accuracy: 0.9690\n", "Epoch 253/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3931 - accuracy: 0.8569\n", "Epoch 00253: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3982 - accuracy: 0.8555 - val_loss: 0.1753 - val_accuracy: 0.9683\n", "Epoch 254/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.3887 - accuracy: 0.8602\n", "Epoch 00254: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3944 - accuracy: 0.8575 - val_loss: 0.1766 - val_accuracy: 0.9705\n", "Epoch 255/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3992 - accuracy: 0.8653\n", "Epoch 00255: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3885 - accuracy: 0.8656 - val_loss: 0.1767 - val_accuracy: 0.9675\n", "Epoch 256/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3829 - accuracy: 0.8591\n", "Epoch 00256: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3933 - accuracy: 0.8593 - val_loss: 0.1748 - val_accuracy: 0.9705\n", "Epoch 257/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3618 - accuracy: 0.8720\n", "Epoch 00257: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3659 - accuracy: 0.8713 - val_loss: 0.1724 - val_accuracy: 0.9705\n", "Epoch 258/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3713 - accuracy: 0.8669\n", "Epoch 00258: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3778 - accuracy: 0.8638 - val_loss: 0.1717 - val_accuracy: 0.9690\n", "Epoch 259/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3893 - accuracy: 0.8609\n", "Epoch 00259: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3861 - accuracy: 0.8623 - val_loss: 0.1704 - val_accuracy: 0.9653\n", "Epoch 260/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4058 - accuracy: 0.8541\n", "Epoch 00260: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4047 - accuracy: 0.8555 - val_loss: 0.1697 - val_accuracy: 0.9698\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 261/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3938 - accuracy: 0.8637\n", "Epoch 00261: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4018 - accuracy: 0.8595 - val_loss: 0.1723 - val_accuracy: 0.9683\n", "Epoch 262/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3979 - accuracy: 0.8621\n", "Epoch 00262: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3969 - accuracy: 0.8610 - val_loss: 0.1716 - val_accuracy: 0.9660\n", "Epoch 263/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3927 - accuracy: 0.8541\n", "Epoch 00263: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3899 - accuracy: 0.8552 - val_loss: 0.1736 - val_accuracy: 0.9683\n", "Epoch 264/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3762 - accuracy: 0.8662\n", "Epoch 00264: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3780 - accuracy: 0.8643 - val_loss: 0.1716 - val_accuracy: 0.9690\n", "Epoch 265/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3918 - accuracy: 0.8612\n", "Epoch 00265: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3974 - accuracy: 0.8578 - val_loss: 0.1706 - val_accuracy: 0.9683\n", "Epoch 266/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3890 - accuracy: 0.8581\n", "Epoch 00266: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3820 - accuracy: 0.8618 - val_loss: 0.1705 - val_accuracy: 0.9683\n", "Epoch 267/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.3909 - accuracy: 0.8582\n", "Epoch 00267: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3994 - accuracy: 0.8532 - val_loss: 0.1732 - val_accuracy: 0.9698\n", "Epoch 268/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.3903 - accuracy: 0.8594\n", "Epoch 00268: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3876 - accuracy: 0.8615 - val_loss: 0.1714 - val_accuracy: 0.9683\n", "Epoch 269/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4032 - accuracy: 0.8547\n", "Epoch 00269: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3948 - accuracy: 0.8580 - val_loss: 0.1709 - val_accuracy: 0.9690\n", "Epoch 270/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3851 - accuracy: 0.8609\n", "Epoch 00270: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3878 - accuracy: 0.8598 - val_loss: 0.1708 - val_accuracy: 0.9675\n", "Epoch 271/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4082 - accuracy: 0.8474\n", "Epoch 00271: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4066 - accuracy: 0.8494 - val_loss: 0.1715 - val_accuracy: 0.9683\n", "Epoch 272/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.3856 - accuracy: 0.8574\n", "Epoch 00272: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3856 - accuracy: 0.8573 - val_loss: 0.1743 - val_accuracy: 0.9698\n", "Epoch 273/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3818 - accuracy: 0.8650\n", "Epoch 00273: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3703 - accuracy: 0.8676 - val_loss: 0.1717 - val_accuracy: 0.9675\n", "Epoch 274/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.4070 - accuracy: 0.8522\n", "Epoch 00274: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3981 - accuracy: 0.8560 - val_loss: 0.1698 - val_accuracy: 0.9690\n", "Epoch 275/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3911 - accuracy: 0.8567\n", "Epoch 00275: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3911 - accuracy: 0.8567 - val_loss: 0.1697 - val_accuracy: 0.9690\n", "Epoch 276/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3829 - accuracy: 0.8628\n", "Epoch 00276: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3829 - accuracy: 0.8628 - val_loss: 0.1688 - val_accuracy: 0.9705\n", "Epoch 277/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3661 - accuracy: 0.8681\n", "Epoch 00277: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3750 - accuracy: 0.8666 - val_loss: 0.1685 - val_accuracy: 0.9683\n", "Epoch 278/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.3851 - accuracy: 0.8556\n", "Epoch 00278: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.3849 - accuracy: 0.8557 - val_loss: 0.1695 - val_accuracy: 0.9698\n", "Epoch 279/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3657 - accuracy: 0.8675\n", "Epoch 00279: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3664 - accuracy: 0.8661 - val_loss: 0.1672 - val_accuracy: 0.9698\n", "Epoch 280/1000\n", "29/32 [==========================>...] - ETA: 0s - loss: 0.3869 - accuracy: 0.8637\n", "Epoch 00280: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3895 - accuracy: 0.8615 - val_loss: 0.1682 - val_accuracy: 0.9698\n", "Epoch 281/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3819 - accuracy: 0.8593\n", "Epoch 00281: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3819 - accuracy: 0.8593 - val_loss: 0.1682 - val_accuracy: 0.9683\n", "Epoch 282/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3578 - accuracy: 0.8775\n", "Epoch 00282: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3634 - accuracy: 0.8721 - val_loss: 0.1655 - val_accuracy: 0.9683\n", "Epoch 283/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3916 - accuracy: 0.8609\n", "Epoch 00283: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3908 - accuracy: 0.8638 - val_loss: 0.1647 - val_accuracy: 0.9698\n", "Epoch 284/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3779 - accuracy: 0.8645\n", "Epoch 00284: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3798 - accuracy: 0.8643 - val_loss: 0.1665 - val_accuracy: 0.9675\n", "Epoch 285/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.3760 - accuracy: 0.8658\n", "Epoch 00285: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3767 - accuracy: 0.8643 - val_loss: 0.1663 - val_accuracy: 0.9683\n", "Epoch 286/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3719 - accuracy: 0.8591\n", "Epoch 00286: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3740 - accuracy: 0.8562 - val_loss: 0.1688 - val_accuracy: 0.9690\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 287/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3942 - accuracy: 0.8609\n", "Epoch 00287: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3916 - accuracy: 0.8605 - val_loss: 0.1672 - val_accuracy: 0.9675\n", "Epoch 288/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.3862 - accuracy: 0.8577\n", "Epoch 00288: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3879 - accuracy: 0.8593 - val_loss: 0.1700 - val_accuracy: 0.9690\n", "Epoch 289/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3733 - accuracy: 0.8625\n", "Epoch 00289: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3798 - accuracy: 0.8608 - val_loss: 0.1680 - val_accuracy: 0.9698\n", "Epoch 290/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3767 - accuracy: 0.8641\n", "Epoch 00290: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3827 - accuracy: 0.8610 - val_loss: 0.1643 - val_accuracy: 0.9683\n", "Epoch 291/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3873 - accuracy: 0.8625\n", "Epoch 00291: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3927 - accuracy: 0.8608 - val_loss: 0.1667 - val_accuracy: 0.9675\n", "Epoch 292/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3734 - accuracy: 0.8606\n", "Epoch 00292: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3780 - accuracy: 0.8595 - val_loss: 0.1688 - val_accuracy: 0.9653\n", "Epoch 293/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3803 - accuracy: 0.8641\n", "Epoch 00293: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3885 - accuracy: 0.8590 - val_loss: 0.1683 - val_accuracy: 0.9660\n", "Epoch 294/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3849 - accuracy: 0.8585\n", "Epoch 00294: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3875 - accuracy: 0.8578 - val_loss: 0.1665 - val_accuracy: 0.9683\n", "Epoch 295/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3917 - accuracy: 0.8544\n", "Epoch 00295: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3914 - accuracy: 0.8527 - val_loss: 0.1677 - val_accuracy: 0.9675\n", "Epoch 296/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3881 - accuracy: 0.8603\n", "Epoch 00296: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3874 - accuracy: 0.8593 - val_loss: 0.1686 - val_accuracy: 0.9690\n", "Epoch 297/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3823 - accuracy: 0.8588\n", "Epoch 00297: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3829 - accuracy: 0.8583 - val_loss: 0.1652 - val_accuracy: 0.9675\n", "Epoch 298/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3711 - accuracy: 0.8606\n", "Epoch 00298: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3771 - accuracy: 0.8590 - val_loss: 0.1657 - val_accuracy: 0.9675\n", "Epoch 299/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3764 - accuracy: 0.8600\n", "Epoch 00299: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3771 - accuracy: 0.8648 - val_loss: 0.1669 - val_accuracy: 0.9698\n", "Epoch 300/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.3796 - accuracy: 0.8602\n", "Epoch 00300: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3816 - accuracy: 0.8585 - val_loss: 0.1641 - val_accuracy: 0.9675\n", "Epoch 301/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3891 - accuracy: 0.8635\n", "Epoch 00301: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3891 - accuracy: 0.8635 - val_loss: 0.1655 - val_accuracy: 0.9683\n", "Epoch 302/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3829 - accuracy: 0.8567\n", "Epoch 00302: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3900 - accuracy: 0.8550 - val_loss: 0.1646 - val_accuracy: 0.9698\n", "Epoch 303/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3580 - accuracy: 0.8634\n", "Epoch 00303: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3671 - accuracy: 0.8595 - val_loss: 0.1645 - val_accuracy: 0.9690\n", "Epoch 304/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3653 - accuracy: 0.8642\n", "Epoch 00304: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3768 - accuracy: 0.8628 - val_loss: 0.1642 - val_accuracy: 0.9675\n", "Epoch 305/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3804 - accuracy: 0.8576\n", "Epoch 00305: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3831 - accuracy: 0.8575 - val_loss: 0.1649 - val_accuracy: 0.9675\n", "Epoch 306/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3689 - accuracy: 0.86 - ETA: 0s - loss: 0.3818 - accuracy: 0.8641\n", "Epoch 00306: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3771 - accuracy: 0.8648 - val_loss: 0.1658 - val_accuracy: 0.9705\n", "Epoch 307/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.4086 - accuracy: 0.8466\n", "Epoch 00307: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4042 - accuracy: 0.8497 - val_loss: 0.1663 - val_accuracy: 0.9690\n", "Epoch 308/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.4046 - accuracy: 0.8606\n", "Epoch 00308: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.4043 - accuracy: 0.8608 - val_loss: 0.1667 - val_accuracy: 0.9683\n", "Epoch 309/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3712 - accuracy: 0.8716\n", "Epoch 00309: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3728 - accuracy: 0.8683 - val_loss: 0.1658 - val_accuracy: 0.9690\n", "Epoch 310/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3840 - accuracy: 0.8587\n", "Epoch 00310: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3871 - accuracy: 0.8580 - val_loss: 0.1650 - val_accuracy: 0.9668\n", "Epoch 311/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3780 - accuracy: 0.8583\n", "Epoch 00311: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3780 - accuracy: 0.8583 - val_loss: 0.1625 - val_accuracy: 0.9690\n", "Epoch 312/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3764 - accuracy: 0.8653\n", "Epoch 00312: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3791 - accuracy: 0.8620 - val_loss: 0.1642 - val_accuracy: 0.9675\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 313/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3702 - accuracy: 0.8725\n", "Epoch 00313: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3745 - accuracy: 0.8673 - val_loss: 0.1671 - val_accuracy: 0.9690\n", "Epoch 314/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3874 - accuracy: 0.8639\n", "Epoch 00314: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3850 - accuracy: 0.8658 - val_loss: 0.1636 - val_accuracy: 0.9705\n", "Epoch 315/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3865 - accuracy: 0.8628\n", "Epoch 00315: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3923 - accuracy: 0.8603 - val_loss: 0.1645 - val_accuracy: 0.9705\n", "Epoch 316/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.3751 - accuracy: 0.8634\n", "Epoch 00316: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3751 - accuracy: 0.8630 - val_loss: 0.1648 - val_accuracy: 0.9698\n", "Epoch 317/1000\n", "27/32 [========================>.....] - ETA: 0s - loss: 0.3950 - accuracy: 0.8576\n", "Epoch 00317: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3887 - accuracy: 0.8598 - val_loss: 0.1630 - val_accuracy: 0.9690\n", "Epoch 318/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3613 - accuracy: 0.8684\n", "Epoch 00318: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3663 - accuracy: 0.8643 - val_loss: 0.1622 - val_accuracy: 0.9675\n", "Epoch 319/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3709 - accuracy: 0.8633\n", "Epoch 00319: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3700 - accuracy: 0.8638 - val_loss: 0.1607 - val_accuracy: 0.9690\n", "Epoch 320/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3685 - accuracy: 0.8594\n", "Epoch 00320: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3713 - accuracy: 0.8595 - val_loss: 0.1633 - val_accuracy: 0.9660\n", "Epoch 321/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3802 - accuracy: 0.8613\n", "Epoch 00321: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3802 - accuracy: 0.8613 - val_loss: 0.1630 - val_accuracy: 0.9683\n", "Epoch 322/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3504 - accuracy: 0.8694\n", "Epoch 00322: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3655 - accuracy: 0.8640 - val_loss: 0.1601 - val_accuracy: 0.9690\n", "Epoch 323/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3704 - accuracy: 0.8603\n", "Epoch 00323: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3704 - accuracy: 0.8603 - val_loss: 0.1600 - val_accuracy: 0.9675\n", "Epoch 324/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3675 - accuracy: 0.8608\n", "Epoch 00324: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.3675 - accuracy: 0.8608 - val_loss: 0.1600 - val_accuracy: 0.9668\n", "Epoch 325/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.3735 - accuracy: 0.8602\n", "Epoch 00325: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3718 - accuracy: 0.8610 - val_loss: 0.1629 - val_accuracy: 0.9690\n", "Epoch 326/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3637 - accuracy: 0.8631\n", "Epoch 00326: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3738 - accuracy: 0.8590 - val_loss: 0.1627 - val_accuracy: 0.9705\n", "Epoch 327/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3681 - accuracy: 0.8691\n", "Epoch 00327: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3681 - accuracy: 0.8691 - val_loss: 0.1621 - val_accuracy: 0.9683\n", "Epoch 328/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3702 - accuracy: 0.8628\n", "Epoch 00328: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3737 - accuracy: 0.8613 - val_loss: 0.1605 - val_accuracy: 0.9675\n", "Epoch 329/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.3924 - accuracy: 0.8602\n", "Epoch 00329: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3923 - accuracy: 0.8600 - val_loss: 0.1633 - val_accuracy: 0.9705\n", "Epoch 330/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3870 - accuracy: 0.8509\n", "Epoch 00330: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3840 - accuracy: 0.8540 - val_loss: 0.1613 - val_accuracy: 0.9713\n", "Epoch 331/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3729 - accuracy: 0.8641\n", "Epoch 00331: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3792 - accuracy: 0.8610 - val_loss: 0.1590 - val_accuracy: 0.9705\n", "Epoch 332/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3809 - accuracy: 0.8634\n", "Epoch 00332: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3784 - accuracy: 0.8666 - val_loss: 0.1589 - val_accuracy: 0.9705\n", "Epoch 333/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 0.3621 - accuracy: 0.8690\n", "Epoch 00333: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.3607 - accuracy: 0.8701 - val_loss: 0.1596 - val_accuracy: 0.9698\n", "Epoch 334/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3794 - accuracy: 0.8576\n", "Epoch 00334: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3713 - accuracy: 0.8605 - val_loss: 0.1589 - val_accuracy: 0.9690\n", "Epoch 335/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.3656 - accuracy: 0.8650\n", "Epoch 00335: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3624 - accuracy: 0.8663 - val_loss: 0.1588 - val_accuracy: 0.9698\n", "Epoch 336/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3751 - accuracy: 0.8609\n", "Epoch 00336: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3773 - accuracy: 0.8600 - val_loss: 0.1557 - val_accuracy: 0.9683\n", "Epoch 337/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 0.3691 - accuracy: 0.8641\n", "Epoch 00337: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.3670 - accuracy: 0.8653 - val_loss: 0.1569 - val_accuracy: 0.9713\n", "Epoch 338/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.3592 - accuracy: 0.8597\n", "Epoch 00338: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3578 - accuracy: 0.8598 - val_loss: 0.1565 - val_accuracy: 0.9713\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Epoch 339/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3769 - accuracy: 0.8633\n", "Epoch 00339: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3769 - accuracy: 0.8633 - val_loss: 0.1553 - val_accuracy: 0.9683\n", "Epoch 340/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3625 - accuracy: 0.8669\n", "Epoch 00340: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3648 - accuracy: 0.8653 - val_loss: 0.1565 - val_accuracy: 0.9698\n", "Epoch 341/1000\n", "26/32 [=======================>......] - ETA: 0s - loss: 0.3605 - accuracy: 0.8669\n", "Epoch 00341: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3588 - accuracy: 0.8688 - val_loss: 0.1580 - val_accuracy: 0.9675\n", "Epoch 342/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3730 - accuracy: 0.8650\n", "Epoch 00342: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3672 - accuracy: 0.8686 - val_loss: 0.1579 - val_accuracy: 0.9675\n", "Epoch 343/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.3742 - accuracy: 0.8616\n", "Epoch 00343: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 8ms/step - loss: 0.3748 - accuracy: 0.8615 - val_loss: 0.1585 - val_accuracy: 0.9660\n", "Epoch 344/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3826 - accuracy: 0.8641\n", "Epoch 00344: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3755 - accuracy: 0.8668 - val_loss: 0.1620 - val_accuracy: 0.9675\n", "Epoch 345/1000\n", "31/32 [============================>.] - ETA: 0s - loss: 0.3707 - accuracy: 0.8644\n", "Epoch 00345: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.3706 - accuracy: 0.8646 - val_loss: 0.1571 - val_accuracy: 0.9698\n", "Epoch 346/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3740 - accuracy: 0.8537\n", "Epoch 00346: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3666 - accuracy: 0.8578 - val_loss: 0.1587 - val_accuracy: 0.9683\n", "Epoch 347/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3786 - accuracy: 0.8583\n", "Epoch 00347: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3786 - accuracy: 0.8583 - val_loss: 0.1588 - val_accuracy: 0.9698\n", "Epoch 348/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3673 - accuracy: 0.8694\n", "Epoch 00348: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3631 - accuracy: 0.8696 - val_loss: 0.1585 - val_accuracy: 0.9690\n", "Epoch 349/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3887 - accuracy: 0.8557\n", "Epoch 00349: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3887 - accuracy: 0.8557 - val_loss: 0.1573 - val_accuracy: 0.9690\n", "Epoch 350/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3770 - accuracy: 0.8575\n", "Epoch 00350: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3770 - accuracy: 0.8575 - val_loss: 0.1573 - val_accuracy: 0.9683\n", "Epoch 351/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3831 - accuracy: 0.8603\n", "Epoch 00351: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3783 - accuracy: 0.8610 - val_loss: 0.1587 - val_accuracy: 0.9683\n", "Epoch 352/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 0.3559 - accuracy: 0.8708\n", "Epoch 00352: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.3571 - accuracy: 0.8698 - val_loss: 0.1571 - val_accuracy: 0.9690\n", "Epoch 353/1000\n", "30/32 [===========================>..] - ETA: 0s - loss: 0.3684 - accuracy: 0.8641\n", "Epoch 00353: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3686 - accuracy: 0.8635 - val_loss: 0.1579 - val_accuracy: 0.9690\n", "Epoch 354/1000\n", "28/32 [=========================>....] - ETA: 0s - loss: 0.3689 - accuracy: 0.8652\n", "Epoch 00354: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3731 - accuracy: 0.8633 - val_loss: 0.1575 - val_accuracy: 0.9705\n", "Epoch 355/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3796 - accuracy: 0.8584\n", "Epoch 00355: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3829 - accuracy: 0.8588 - val_loss: 0.1584 - val_accuracy: 0.9675\n", "Epoch 356/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3776 - accuracy: 0.8591\n", "Epoch 00356: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 12ms/step - loss: 0.3737 - accuracy: 0.8623 - val_loss: 0.1568 - val_accuracy: 0.9675\n", "Epoch 357/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3539 - accuracy: 0.8697\n", "Epoch 00357: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 10ms/step - loss: 0.3696 - accuracy: 0.8635 - val_loss: 0.1571 - val_accuracy: 0.9683\n", "Epoch 358/1000\n", "32/32 [==============================] - ETA: 0s - loss: 0.3729 - accuracy: 0.8671\n", "Epoch 00358: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3729 - accuracy: 0.8671 - val_loss: 0.1568 - val_accuracy: 0.9675\n", "Epoch 359/1000\n", "25/32 [======================>.......] - ETA: 0s - loss: 0.3893 - accuracy: 0.8666\n", "Epoch 00359: saving model to model/point_history_classifier\\point_history_classifier.hdf5\n", "32/32 [==============================] - 0s 11ms/step - loss: 0.3871 - accuracy: 0.8666 - val_loss: 0.1558 - val_accuracy: 0.9683\n", "Epoch 00359: early stopping\n"]}, {"data": {"text/plain": ["<tensorflow.python.keras.callbacks.History at 0x1e1def6c460>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["model.fit(\n", "    X_train,\n", "    y_train,\n", "    epochs=1000,\n", "    batch_size=128,\n", "    validation_data=(X_test, y_test),\n", "    callbacks=[cp_callback, es_callback]\n", ")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["# 保存したモデルのロード\n", "model = tf.keras.models.load_model(model_save_path)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.9438375  0.01624594 0.01188833 0.02802826]\n", "0\n"]}], "source": ["# 推論テスト\n", "predict_result = model.predict(np.array([X_test[0]]))\n", "print(np.squeeze(predict_result))\n", "print(np.argmax(np.squeeze(predict_result)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 混同行列"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 504x432 with 2 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Classification Report\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.99      1.00      0.99       395\n", "           1       0.93      0.99      0.96       295\n", "           2       0.98      0.97      0.98       307\n", "           3       0.96      0.91      0.93       327\n", "\n", "    accuracy                           0.97      1324\n", "   macro avg       0.97      0.97      0.97      1324\n", "weighted avg       0.97      0.97      0.97      1324\n", "\n"]}], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "\n", "def print_confusion_matrix(y_true, y_pred, report=True):\n", "    labels = sorted(list(set(y_true)))\n", "    cmx_data = confusion_matrix(y_true, y_pred, labels=labels)\n", "    \n", "    df_cmx = pd.DataFrame(cmx_data, index=labels, columns=labels)\n", " \n", "    fig, ax = plt.subplots(figsize=(7, 6))\n", "    sns.heatmap(df_cmx, annot=True, fmt='g' ,square=False)\n", "    ax.set_ylim(len(set(y_true)), 0)\n", "    plt.show()\n", "    \n", "    if report:\n", "        print('Classification Report')\n", "        print(classification_report(y_test, y_pred))\n", "\n", "Y_pred = model.predict(X_test)\n", "y_pred = np.argmax(Y_pred, axis=1)\n", "\n", "print_confusion_matrix(y_test, y_pred)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tensorflow-Lite用のモデルへ変換"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 推論専用のモデルとして保存\n", "model.save(model_save_path, include_optimizer=False)\n", "model = tf.keras.models.load_model(model_save_path)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["tflite_save_path = 'model/point_history_classifier/point_history_classifier.tflite'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# モデルを変換(量子化\n", "converter = tf.lite.TFLiteConverter.from_keras_model(model)  # converter = tf.lite.TFLiteConverter.from_saved_model(saved_model_path)\n", "converter.optimizations = [tf.lite.Optimize.DEFAULT]\n", "tflite_quantized_model = converter.convert()\n", "\n", "open(tflite_save_path, 'wb').write(tflite_quantized_model)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 推論テスト"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["interpreter = tf.lite.Interpreter(model_path=tflite_save_path)\n", "interpreter.allocate_tensors()"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'name': 'input_1', 'index': 0, 'shape': array([ 1, 32]), 'shape_signature': array([-1, 32]), 'dtype': <class 'numpy.float32'>, 'quantization': (0.0, 0), 'quantization_parameters': {'scales': array([], dtype=float32), 'zero_points': array([], dtype=int32), 'quantized_dimension': 0}, 'sparsity_parameters': {}}]\n"]}], "source": ["# 入出力テンソルを取得\n", "input_details = interpreter.get_input_details()\n", "output_details = interpreter.get_output_details()\n", "print(input_details)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["interpreter.set_tensor(input_details[0]['index'], np.array([X_test[0]]))"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 0 ns\n"]}], "source": ["%%time\n", "# 推論実施\n", "interpreter.invoke()\n", "tflite_results = interpreter.get_tensor(output_details[0]['index'])"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[0.9579909  0.01342559 0.00907356 0.01950999]\n", "0\n"]}], "source": ["print(np.squeeze(tflite_results))\n", "print(np.argmax(np.squeeze(tflite_results)))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.5"}}, "nbformat": 4, "nbformat_minor": 4}