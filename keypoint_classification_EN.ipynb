{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "igMyGnjE9hEp"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\miniconda3\\envs\\gaussian_splatting\\lib\\site-packages\\tensorboard\\compat\\tensorflow_stub\\dtypes.py:541: FutureWarning: Passing (type, 1) or '1type' as a synonym of type is deprecated; in a future version of numpy, it will be understood as (type, (1,)) / '(1,)type'.\n", "  _np_qint8 = np.dtype([(\"qint8\", np.int8, 1)])\n", "c:\\Users\\<USER>\\miniconda3\\envs\\gaussian_splatting\\lib\\site-packages\\tensorboard\\compat\\tensorflow_stub\\dtypes.py:542: FutureWarning: Passing (type, 1) or '1type' as a synonym of type is deprecated; in a future version of numpy, it will be understood as (type, (1,)) / '(1,)type'.\n", "  _np_quint8 = np.dtype([(\"quint8\", np.uint8, 1)])\n", "c:\\Users\\<USER>\\miniconda3\\envs\\gaussian_splatting\\lib\\site-packages\\tensorboard\\compat\\tensorflow_stub\\dtypes.py:543: FutureWarning: Passing (type, 1) or '1type' as a synonym of type is deprecated; in a future version of numpy, it will be understood as (type, (1,)) / '(1,)type'.\n", "  _np_qint16 = np.dtype([(\"qint16\", np.int16, 1)])\n", "c:\\Users\\<USER>\\miniconda3\\envs\\gaussian_splatting\\lib\\site-packages\\tensorboard\\compat\\tensorflow_stub\\dtypes.py:544: FutureWarning: Passing (type, 1) or '1type' as a synonym of type is deprecated; in a future version of numpy, it will be understood as (type, (1,)) / '(1,)type'.\n", "  _np_quint16 = np.dtype([(\"quint16\", np.uint16, 1)])\n", "c:\\Users\\<USER>\\miniconda3\\envs\\gaussian_splatting\\lib\\site-packages\\tensorboard\\compat\\tensorflow_stub\\dtypes.py:545: FutureWarning: Passing (type, 1) or '1type' as a synonym of type is deprecated; in a future version of numpy, it will be understood as (type, (1,)) / '(1,)type'.\n", "  _np_qint32 = np.dtype([(\"qint32\", np.int32, 1)])\n", "c:\\Users\\<USER>\\miniconda3\\envs\\gaussian_splatting\\lib\\site-packages\\tensorboard\\compat\\tensorflow_stub\\dtypes.py:550: FutureWarning: Passing (type, 1) or '1type' as a synonym of type is deprecated; in a future version of numpy, it will be understood as (type, (1,)) / '(1,)type'.\n", "  np_resource = np.dtype([(\"resource\", np.ubyte, 1)])\n"]}], "source": ["import csv\n", "\n", "import numpy as np\n", "import tensorflow as tf\n", "from sklearn.model_selection import train_test_split\n", "\n", "RANDOM_SEED = 42"]}, {"cell_type": "markdown", "metadata": {"id": "t2HDvhIu9hEr"}, "source": ["# Specify each path"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "9NvZP2Zn9hEy"}, "outputs": [], "source": ["dataset = 'model/keypoint_classifier/keypoint.csv'\n", "model_save_path = 'model/keypoint_classifier/keypoint_classifier.hdf5'\n", "tflite_save_path = 'model/keypoint_classifier/keypoint_classifier.tflite'"]}, {"cell_type": "markdown", "metadata": {"id": "s5oMH7x19hEz"}, "source": ["# Set number of classes"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"id": "du4kodXL9hEz"}, "outputs": [], "source": ["NUM_CLASSES = 6"]}, {"cell_type": "markdown", "metadata": {"id": "XjnL0uso9hEz"}, "source": ["# Dataset reading"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"id": "QT5ZqtEz9hE0"}, "outputs": [], "source": ["X_dataset = np.loadtxt(dataset, delimiter=',', dtype='float32', usecols=list(range(1, (21 * 2) + 1)))"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"id": "QmoKFsp49hE0"}, "outputs": [], "source": ["y_dataset = np.loadtxt(dataset, delimiter=',', dtype='int32', usecols=(0))"]}, {"cell_type": "code", "execution_count": 6, "metadata": {"id": "xQU7JTZ_9hE0"}, "outputs": [], "source": ["X_train, X_test, y_train, y_test = train_test_split(X_dataset, y_dataset, train_size=0.75, random_state=RANDOM_SEED)"]}, {"cell_type": "markdown", "metadata": {"id": "mxK_lETT9hE0"}, "source": ["# Model building"]}, {"cell_type": "code", "execution_count": 7, "metadata": {"id": "vHBmUf1t9hE1"}, "outputs": [], "source": ["model = tf.keras.models.Sequential([\n", "    tf.keras.layers.Input((21 * 2, )),\n", "    tf.keras.layers.Dropout(0.2),\n", "    tf.keras.layers.Dense(20, activation='relu'),\n", "    tf.keras.layers.Dropout(0.4),\n", "    tf.keras.layers.Dense(10, activation='relu'),\n", "    tf.keras.layers.Dense(NUM_CLASSES, activation='softmax')\n", "])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ypqky9tc9hE1", "outputId": "5db082bb-30e3-4110-bf63-a1ee777ecd46"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Model: \"sequential\"\n", "_________________________________________________________________\n", " Layer (type)                Output Shape              Param #   \n", "=================================================================\n", " dropout (Dropout)           (None, 42)                0         \n", "                                                                 \n", " dense (Dense)               (None, 20)                860       \n", "                                                                 \n", " dropout_1 (Dropout)         (None, 20)                0         \n", "                                                                 \n", " dense_1 (<PERSON><PERSON>)             (None, 10)                210       \n", "                                                                 \n", " dense_2 (<PERSON><PERSON>)             (<PERSON>, 6)                 66        \n", "                                                                 \n", "=================================================================\n", "Total params: 1,136\n", "Trainable params: 1,136\n", "Non-trainable params: 0\n", "_________________________________________________________________\n"]}], "source": ["model.summary()  # tf.keras.utils.plot_model(model, show_shapes=True)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"id": "MbMjOflQ9hE1"}, "outputs": [], "source": ["# Model checkpoint callback\n", "cp_callback = tf.keras.callbacks.ModelCheckpoint(\n", "    model_save_path, verbose=1, save_weights_only=False)\n", "# Callback for early stopping\n", "es_callback = tf.keras.callbacks.EarlyStopping(patience=20, verbose=1)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"id": "c3Dac0M_9hE2"}, "outputs": [], "source": ["# Model compilation\n", "model.compile(\n", "    optimizer='adam',\n", "    loss='sparse_categorical_crossentropy',\n", "    metrics=['accuracy']\n", ")"]}, {"cell_type": "markdown", "metadata": {"id": "7XI0j1Iu9hE2"}, "source": ["# Model training"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "WirBl-JE9hE3", "outputId": "71b30ca2-8294-4d9d-8aa2-800d90d399de", "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Epoch 1/1000\n", "1/7 [===>..........................] - ETA: 3s - loss: 1.9442 - accuracy: 0.1719\n", "Epoch 1: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 1s 38ms/step - loss: 1.9193 - accuracy: 0.1942 - val_loss: 1.7753 - val_accuracy: 0.2609\n", "Epoch 2/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.8237 - accuracy: 0.2422\n", "Epoch 2: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 1.8229 - accuracy: 0.2132 - val_loss: 1.7091 - val_accuracy: 0.3913\n", "Epoch 3/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.8147 - accuracy: 0.2578\n", "Epoch 3: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.7774 - accuracy: 0.2388 - val_loss: 1.6531 - val_accuracy: 0.4615\n", "Epoch 4/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.7380 - accuracy: 0.3438\n", "Epoch 4: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 1.7148 - accuracy: 0.3136 - val_loss: 1.6047 - val_accuracy: 0.5050\n", "Epoch 5/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.7054 - accuracy: 0.3281\n", "Epoch 5: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 1.6841 - accuracy: 0.3404 - val_loss: 1.5598 - val_accuracy: 0.5518\n", "Epoch 6/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.6379 - accuracy: 0.3594\n", "Epoch 6: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.6560 - accuracy: 0.3516 - val_loss: 1.5211 - val_accuracy: 0.5753\n", "Epoch 7/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.6077 - accuracy: 0.3594\n", "Epoch 7: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 1.6126 - accuracy: 0.3895 - val_loss: 1.4824 - val_accuracy: 0.5886\n", "Epoch 8/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.5545 - accuracy: 0.4531\n", "Epoch 8: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.5633 - accuracy: 0.4107 - val_loss: 1.4451 - val_accuracy: 0.5953\n", "Epoch 9/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.5648 - accuracy: 0.3984\n", "Epoch 9: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 1.5516 - accuracy: 0.4163 - val_loss: 1.4083 - val_accuracy: 0.6221\n", "Epoch 10/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.5379 - accuracy: 0.3594\n", "Epoch 10: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 1.5279 - accuracy: 0.4297 - val_loss: 1.3723 - val_accuracy: 0.6421\n", "Epoch 11/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.5352 - accuracy: 0.4375\n", "Epoch 11: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 1.5023 - accuracy: 0.4598 - val_loss: 1.3369 - val_accuracy: 0.6488\n", "Epoch 12/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.4997 - accuracy: 0.4844\n", "Epoch 12: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 1.4872 - accuracy: 0.4688 - val_loss: 1.3023 - val_accuracy: 0.6522\n", "Epoch 13/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.4516 - accuracy: 0.4922\n", "Epoch 13: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.4344 - accuracy: 0.4911 - val_loss: 1.2709 - val_accuracy: 0.6522\n", "Epoch 14/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.4504 - accuracy: 0.4609\n", "Epoch 14: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.4208 - accuracy: 0.4922 - val_loss: 1.2413 - val_accuracy: 0.6656\n", "Epoch 15/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.2992 - accuracy: 0.5938\n", "Epoch 15: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.3942 - accuracy: 0.5089 - val_loss: 1.2130 - val_accuracy: 0.6656\n", "Epoch 16/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.2902 - accuracy: 0.5547\n", "Epoch 16: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 1.3519 - accuracy: 0.5201 - val_loss: 1.1809 - val_accuracy: 0.6756\n", "Epoch 17/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.3246 - accuracy: 0.6016\n", "Epoch 17: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.3175 - accuracy: 0.5603 - val_loss: 1.1474 - val_accuracy: 0.6789\n", "Epoch 18/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.4036 - accuracy: 0.5469\n", "Epoch 18: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.3284 - accuracy: 0.5480 - val_loss: 1.1152 - val_accuracy: 0.6789\n", "Epoch 19/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.2928 - accuracy: 0.5625\n", "Epoch 19: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 14ms/step - loss: 1.2961 - accuracy: 0.5469 - val_loss: 1.0838 - val_accuracy: 0.6789\n", "Epoch 20/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.2896 - accuracy: 0.5703\n", "Epoch 20: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.2450 - accuracy: 0.5714 - val_loss: 1.0529 - val_accuracy: 0.6789\n", "Epoch 21/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.2568 - accuracy: 0.5547\n", "Epoch 21: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.2300 - accuracy: 0.5815 - val_loss: 1.0239 - val_accuracy: 0.6789\n", "Epoch 22/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.1927 - accuracy: 0.6016\n", "Epoch 22: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 11ms/step - loss: 1.2447 - accuracy: 0.5592 - val_loss: 0.9974 - val_accuracy: 0.6789\n", "Epoch 23/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.2106 - accuracy: 0.5547\n", "Epoch 23: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.1714 - accuracy: 0.6004 - val_loss: 0.9680 - val_accuracy: 0.6789\n", "Epoch 24/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.2460 - accuracy: 0.5469\n", "Epoch 24: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 1.1896 - accuracy: 0.5815 - val_loss: 0.9360 - val_accuracy: 0.6823\n", "Epoch 25/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.1851 - accuracy: 0.5781\n", "Epoch 25: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.1464 - accuracy: 0.5971 - val_loss: 0.9052 - val_accuracy: 0.6823\n", "Epoch 26/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.1797 - accuracy: 0.6172\n", "Epoch 26: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 1.1168 - accuracy: 0.6060 - val_loss: 0.8750 - val_accuracy: 0.6856\n", "Epoch 27/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.1339 - accuracy: 0.6172\n", "Epoch 27: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 1.0880 - accuracy: 0.6283 - val_loss: 0.8471 - val_accuracy: 0.6890\n", "Epoch 28/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.0502 - accuracy: 0.6016\n", "Epoch 28: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 11ms/step - loss: 1.0853 - accuracy: 0.6049 - val_loss: 0.8180 - val_accuracy: 0.6890\n", "Epoch 29/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.0407 - accuracy: 0.6094\n", "Epoch 29: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 1.0435 - accuracy: 0.6261 - val_loss: 0.7899 - val_accuracy: 0.7124\n", "Epoch 30/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.0895 - accuracy: 0.6172\n", "Epoch 30: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 1.0412 - accuracy: 0.6384 - val_loss: 0.7619 - val_accuracy: 0.7258\n", "Epoch 31/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 1.0000 - accuracy: 0.6562\n", "Epoch 31: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.9902 - accuracy: 0.6562 - val_loss: 0.7344 - val_accuracy: 0.7592\n", "Epoch 32/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.9424 - accuracy: 0.6875\n", "Epoch 32: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.9702 - accuracy: 0.6719 - val_loss: 0.7058 - val_accuracy: 0.7793\n", "Epoch 33/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.9334 - accuracy: 0.7422\n", "Epoch 33: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.9373 - accuracy: 0.6842 - val_loss: 0.6791 - val_accuracy: 0.7759\n", "Epoch 34/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.9473 - accuracy: 0.6797\n", "Epoch 34: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.9203 - accuracy: 0.6819 - val_loss: 0.6531 - val_accuracy: 0.7860\n", "Epoch 35/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.9150 - accuracy: 0.6562\n", "Epoch 35: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.9256 - accuracy: 0.6596 - val_loss: 0.6249 - val_accuracy: 0.8629\n", "Epoch 36/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.8382 - accuracy: 0.7578\n", "Epoch 36: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.8524 - accuracy: 0.6987 - val_loss: 0.6003 - val_accuracy: 0.8896\n", "Epoch 37/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.8334 - accuracy: 0.7031\n", "Epoch 37: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 11ms/step - loss: 0.8343 - accuracy: 0.6975 - val_loss: 0.5776 - val_accuracy: 0.9030\n", "Epoch 38/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.8295 - accuracy: 0.6875\n", "Epoch 38: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.8332 - accuracy: 0.6942 - val_loss: 0.5584 - val_accuracy: 0.9030\n", "Epoch 39/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.8679 - accuracy: 0.6875\n", "Epoch 39: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.8215 - accuracy: 0.7087 - val_loss: 0.5379 - val_accuracy: 0.9164\n", "Epoch 40/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.8808 - accuracy: 0.6953\n", "Epoch 40: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.8254 - accuracy: 0.7065 - val_loss: 0.5183 - val_accuracy: 0.9164\n", "Epoch 41/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.8323 - accuracy: 0.6953\n", "Epoch 41: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.8052 - accuracy: 0.6931 - val_loss: 0.4982 - val_accuracy: 0.9197\n", "Epoch 42/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.7318 - accuracy: 0.7500\n", "Epoch 42: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.7646 - accuracy: 0.7065 - val_loss: 0.4801 - val_accuracy: 0.9197\n", "Epoch 43/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.7208 - accuracy: 0.7891\n", "Epoch 43: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.7746 - accuracy: 0.7366 - val_loss: 0.4620 - val_accuracy: 0.9030\n", "Epoch 44/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.8476 - accuracy: 0.6328\n", "Epoch 44: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.7567 - accuracy: 0.7277 - val_loss: 0.4464 - val_accuracy: 0.9064\n", "Epoch 45/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.7717 - accuracy: 0.7109\n", "Epoch 45: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.7788 - accuracy: 0.7020 - val_loss: 0.4322 - val_accuracy: 0.9064\n", "Epoch 46/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.7097 - accuracy: 0.7578\n", "Epoch 46: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.7302 - accuracy: 0.7333 - val_loss: 0.4187 - val_accuracy: 0.9097\n", "Epoch 47/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.7158 - accuracy: 0.7500\n", "Epoch 47: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.7115 - accuracy: 0.7433 - val_loss: 0.4070 - val_accuracy: 0.9097\n", "Epoch 48/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.7314 - accuracy: 0.7266\n", "Epoch 48: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.6987 - accuracy: 0.7522 - val_loss: 0.3952 - val_accuracy: 0.9231\n", "Epoch 49/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.7106 - accuracy: 0.7500\n", "Epoch 49: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.6958 - accuracy: 0.7467 - val_loss: 0.3844 - val_accuracy: 0.9264\n", "Epoch 50/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.7152 - accuracy: 0.7188\n", "Epoch 50: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.7089 - accuracy: 0.7444 - val_loss: 0.3742 - val_accuracy: 0.9231\n", "Epoch 51/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.8132 - accuracy: 0.6562\n", "Epoch 51: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.7028 - accuracy: 0.7232 - val_loss: 0.3668 - val_accuracy: 0.9331\n", "Epoch 52/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.7293 - accuracy: 0.7500\n", "Epoch 52: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 14ms/step - loss: 0.6985 - accuracy: 0.7400 - val_loss: 0.3599 - val_accuracy: 0.9398\n", "Epoch 53/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6565 - accuracy: 0.8125\n", "Epoch 53: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.6702 - accuracy: 0.7567 - val_loss: 0.3518 - val_accuracy: 0.9431\n", "Epoch 54/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5737 - accuracy: 0.7969\n", "Epoch 54: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.6708 - accuracy: 0.7511 - val_loss: 0.3456 - val_accuracy: 0.9799\n", "Epoch 55/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6865 - accuracy: 0.7578\n", "Epoch 55: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 11ms/step - loss: 0.6415 - accuracy: 0.7500 - val_loss: 0.3377 - val_accuracy: 0.9632\n", "Epoch 56/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5143 - accuracy: 0.8438\n", "Epoch 56: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.6398 - accuracy: 0.7679 - val_loss: 0.3294 - val_accuracy: 0.9699\n", "Epoch 57/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6038 - accuracy: 0.8203\n", "Epoch 57: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.6195 - accuracy: 0.7723 - val_loss: 0.3223 - val_accuracy: 0.9833\n", "Epoch 58/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.7169 - accuracy: 0.7500\n", "Epoch 58: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.6412 - accuracy: 0.7768 - val_loss: 0.3131 - val_accuracy: 0.9732\n", "Epoch 59/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6780 - accuracy: 0.7344\n", "Epoch 59: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.6476 - accuracy: 0.7489 - val_loss: 0.3063 - val_accuracy: 0.9632\n", "Epoch 60/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6623 - accuracy: 0.7266\n", "Epoch 60: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.6570 - accuracy: 0.7500 - val_loss: 0.2996 - val_accuracy: 0.9565\n", "Epoch 61/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6778 - accuracy: 0.7188\n", "Epoch 61: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.5897 - accuracy: 0.7879 - val_loss: 0.2954 - val_accuracy: 0.9766\n", "Epoch 62/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5551 - accuracy: 0.7734\n", "Epoch 62: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.6049 - accuracy: 0.7656 - val_loss: 0.2896 - val_accuracy: 0.9766\n", "Epoch 63/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6546 - accuracy: 0.7734\n", "Epoch 63: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.5824 - accuracy: 0.7801 - val_loss: 0.2826 - val_accuracy: 0.9766\n", "Epoch 64/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5281 - accuracy: 0.8125\n", "Epoch 64: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.5683 - accuracy: 0.7980 - val_loss: 0.2758 - val_accuracy: 0.9666\n", "Epoch 65/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6284 - accuracy: 0.7578\n", "Epoch 65: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.6138 - accuracy: 0.7690 - val_loss: 0.2720 - val_accuracy: 0.9766\n", "Epoch 66/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5647 - accuracy: 0.8047\n", "Epoch 66: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.5933 - accuracy: 0.7969 - val_loss: 0.2683 - val_accuracy: 0.9766\n", "Epoch 67/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6028 - accuracy: 0.7500\n", "Epoch 67: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 12ms/step - loss: 0.5541 - accuracy: 0.8080 - val_loss: 0.2634 - val_accuracy: 0.9833\n", "Epoch 68/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5850 - accuracy: 0.8125\n", "Epoch 68: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.5951 - accuracy: 0.7891 - val_loss: 0.2606 - val_accuracy: 1.0000\n", "Epoch 69/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5278 - accuracy: 0.8047\n", "Epoch 69: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.5639 - accuracy: 0.8047 - val_loss: 0.2557 - val_accuracy: 1.0000\n", "Epoch 70/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5258 - accuracy: 0.7891\n", "Epoch 70: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.5720 - accuracy: 0.7902 - val_loss: 0.2510 - val_accuracy: 1.0000\n", "Epoch 71/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5311 - accuracy: 0.8125\n", "Epoch 71: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.5756 - accuracy: 0.7790 - val_loss: 0.2449 - val_accuracy: 0.9866\n", "Epoch 72/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5572 - accuracy: 0.8047\n", "Epoch 72: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.5747 - accuracy: 0.7946 - val_loss: 0.2409 - val_accuracy: 0.9866\n", "Epoch 73/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4876 - accuracy: 0.8203\n", "Epoch 73: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.5470 - accuracy: 0.7969 - val_loss: 0.2396 - val_accuracy: 1.0000\n", "Epoch 74/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5127 - accuracy: 0.8125\n", "Epoch 74: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 11ms/step - loss: 0.5430 - accuracy: 0.8192 - val_loss: 0.2367 - val_accuracy: 1.0000\n", "Epoch 75/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4798 - accuracy: 0.8359\n", "Epoch 75: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.5507 - accuracy: 0.7857 - val_loss: 0.2339 - val_accuracy: 1.0000\n", "Epoch 76/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5230 - accuracy: 0.7812\n", "Epoch 76: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.5451 - accuracy: 0.7924 - val_loss: 0.2288 - val_accuracy: 1.0000\n", "Epoch 77/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5141 - accuracy: 0.7734\n", "Epoch 77: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.5308 - accuracy: 0.7902 - val_loss: 0.2244 - val_accuracy: 0.9933\n", "Epoch 78/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5507 - accuracy: 0.7734\n", "Epoch 78: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.5357 - accuracy: 0.7991 - val_loss: 0.2201 - val_accuracy: 0.9967\n", "Epoch 79/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6142 - accuracy: 0.8125\n", "Epoch 79: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.5390 - accuracy: 0.7879 - val_loss: 0.2169 - val_accuracy: 0.9967\n", "Epoch 80/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6055 - accuracy: 0.7656\n", "Epoch 80: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.5506 - accuracy: 0.7969 - val_loss: 0.2144 - val_accuracy: 1.0000\n", "Epoch 81/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4904 - accuracy: 0.8438\n", "Epoch 81: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.5362 - accuracy: 0.7913 - val_loss: 0.2132 - val_accuracy: 1.0000\n", "Epoch 82/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6039 - accuracy: 0.7969\n", "Epoch 82: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.5248 - accuracy: 0.8069 - val_loss: 0.2111 - val_accuracy: 1.0000\n", "Epoch 83/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5269 - accuracy: 0.8281\n", "Epoch 83: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.5320 - accuracy: 0.8013 - val_loss: 0.2102 - val_accuracy: 1.0000\n", "Epoch 84/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6857 - accuracy: 0.8047\n", "Epoch 84: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.5648 - accuracy: 0.8092 - val_loss: 0.2104 - val_accuracy: 1.0000\n", "Epoch 85/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4768 - accuracy: 0.8203\n", "Epoch 85: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 6ms/step - loss: 0.5157 - accuracy: 0.8103 - val_loss: 0.2076 - val_accuracy: 1.0000\n", "Epoch 86/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5771 - accuracy: 0.7812\n", "Epoch 86: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.5297 - accuracy: 0.7991 - val_loss: 0.2043 - val_accuracy: 1.0000\n", "Epoch 87/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5164 - accuracy: 0.8125\n", "Epoch 87: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.5280 - accuracy: 0.8192 - val_loss: 0.2036 - val_accuracy: 1.0000\n", "Epoch 88/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4761 - accuracy: 0.8516\n", "Epoch 88: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4899 - accuracy: 0.8181 - val_loss: 0.2029 - val_accuracy: 1.0000\n", "Epoch 89/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5291 - accuracy: 0.8125\n", "Epoch 89: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.5194 - accuracy: 0.8170 - val_loss: 0.2024 - val_accuracy: 1.0000\n", "Epoch 90/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4963 - accuracy: 0.7734\n", "Epoch 90: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.4997 - accuracy: 0.8047 - val_loss: 0.1972 - val_accuracy: 1.0000\n", "Epoch 91/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4385 - accuracy: 0.8516\n", "Epoch 91: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.5132 - accuracy: 0.8248 - val_loss: 0.1923 - val_accuracy: 1.0000\n", "Epoch 92/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5766 - accuracy: 0.8047\n", "Epoch 92: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.5374 - accuracy: 0.8080 - val_loss: 0.1869 - val_accuracy: 1.0000\n", "Epoch 93/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4662 - accuracy: 0.8594\n", "Epoch 93: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.4836 - accuracy: 0.8426 - val_loss: 0.1818 - val_accuracy: 1.0000\n", "Epoch 94/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5739 - accuracy: 0.7422\n", "Epoch 94: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 13ms/step - loss: 0.4767 - accuracy: 0.8281 - val_loss: 0.1787 - val_accuracy: 1.0000\n", "Epoch 95/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4121 - accuracy: 0.8516\n", "Epoch 95: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 11ms/step - loss: 0.4683 - accuracy: 0.8382 - val_loss: 0.1765 - val_accuracy: 1.0000\n", "Epoch 96/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5038 - accuracy: 0.8281\n", "Epoch 96: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.4951 - accuracy: 0.8304 - val_loss: 0.1733 - val_accuracy: 1.0000\n", "Epoch 97/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5596 - accuracy: 0.7969\n", "Epoch 97: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 11ms/step - loss: 0.5043 - accuracy: 0.8248 - val_loss: 0.1719 - val_accuracy: 1.0000\n", "Epoch 98/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5722 - accuracy: 0.7656\n", "Epoch 98: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4965 - accuracy: 0.8192 - val_loss: 0.1709 - val_accuracy: 1.0000\n", "Epoch 99/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5746 - accuracy: 0.7969\n", "Epoch 99: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.5043 - accuracy: 0.8225 - val_loss: 0.1688 - val_accuracy: 1.0000\n", "Epoch 100/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4603 - accuracy: 0.8750\n", "Epoch 100: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4943 - accuracy: 0.8181 - val_loss: 0.1671 - val_accuracy: 1.0000\n", "Epoch 101/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4872 - accuracy: 0.8125\n", "Epoch 101: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 11ms/step - loss: 0.5123 - accuracy: 0.8103 - val_loss: 0.1641 - val_accuracy: 1.0000\n", "Epoch 102/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4347 - accuracy: 0.8516\n", "Epoch 102: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 6ms/step - loss: 0.4613 - accuracy: 0.8281 - val_loss: 0.1622 - val_accuracy: 1.0000\n", "Epoch 103/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5637 - accuracy: 0.8281\n", "Epoch 103: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4806 - accuracy: 0.8315 - val_loss: 0.1614 - val_accuracy: 1.0000\n", "Epoch 104/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4554 - accuracy: 0.8438\n", "Epoch 104: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 11ms/step - loss: 0.4891 - accuracy: 0.8337 - val_loss: 0.1598 - val_accuracy: 1.0000\n", "Epoch 105/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4915 - accuracy: 0.8750\n", "Epoch 105: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 13ms/step - loss: 0.4549 - accuracy: 0.8449 - val_loss: 0.1565 - val_accuracy: 1.0000\n", "Epoch 106/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4612 - accuracy: 0.8438\n", "Epoch 106: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 11ms/step - loss: 0.4684 - accuracy: 0.8170 - val_loss: 0.1529 - val_accuracy: 1.0000\n", "Epoch 107/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4566 - accuracy: 0.7969\n", "Epoch 107: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 15ms/step - loss: 0.4584 - accuracy: 0.8304 - val_loss: 0.1517 - val_accuracy: 1.0000\n", "Epoch 108/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4349 - accuracy: 0.8359\n", "Epoch 108: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4756 - accuracy: 0.8359 - val_loss: 0.1496 - val_accuracy: 1.0000\n", "Epoch 109/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5392 - accuracy: 0.7891\n", "Epoch 109: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.4954 - accuracy: 0.8147 - val_loss: 0.1521 - val_accuracy: 1.0000\n", "Epoch 110/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5755 - accuracy: 0.8047\n", "Epoch 110: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 11ms/step - loss: 0.4626 - accuracy: 0.8326 - val_loss: 0.1540 - val_accuracy: 0.9967\n", "Epoch 111/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6034 - accuracy: 0.7734\n", "Epoch 111: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.4939 - accuracy: 0.8225 - val_loss: 0.1509 - val_accuracy: 1.0000\n", "Epoch 112/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5323 - accuracy: 0.8516\n", "Epoch 112: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.4720 - accuracy: 0.8382 - val_loss: 0.1478 - val_accuracy: 1.0000\n", "Epoch 113/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5412 - accuracy: 0.8203\n", "Epoch 113: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4532 - accuracy: 0.8304 - val_loss: 0.1478 - val_accuracy: 1.0000\n", "Epoch 114/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4261 - accuracy: 0.8828\n", "Epoch 114: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.4875 - accuracy: 0.8315 - val_loss: 0.1473 - val_accuracy: 1.0000\n", "Epoch 115/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5021 - accuracy: 0.8047\n", "Epoch 115: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4445 - accuracy: 0.8337 - val_loss: 0.1422 - val_accuracy: 1.0000\n", "Epoch 116/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4315 - accuracy: 0.8359\n", "Epoch 116: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4377 - accuracy: 0.8449 - val_loss: 0.1414 - val_accuracy: 1.0000\n", "Epoch 117/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4424 - accuracy: 0.8203\n", "Epoch 117: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.4648 - accuracy: 0.8359 - val_loss: 0.1403 - val_accuracy: 1.0000\n", "Epoch 118/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5237 - accuracy: 0.8125\n", "Epoch 118: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4520 - accuracy: 0.8359 - val_loss: 0.1396 - val_accuracy: 1.0000\n", "Epoch 119/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4109 - accuracy: 0.8672\n", "Epoch 119: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4620 - accuracy: 0.8326 - val_loss: 0.1367 - val_accuracy: 1.0000\n", "Epoch 120/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4174 - accuracy: 0.8438\n", "Epoch 120: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 15ms/step - loss: 0.4273 - accuracy: 0.8527 - val_loss: 0.1325 - val_accuracy: 1.0000\n", "Epoch 121/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3569 - accuracy: 0.8750\n", "Epoch 121: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.4344 - accuracy: 0.8438 - val_loss: 0.1312 - val_accuracy: 1.0000\n", "Epoch 122/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3868 - accuracy: 0.8359\n", "Epoch 122: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4566 - accuracy: 0.8337 - val_loss: 0.1322 - val_accuracy: 1.0000\n", "Epoch 123/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5671 - accuracy: 0.7656\n", "Epoch 123: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.4425 - accuracy: 0.8382 - val_loss: 0.1321 - val_accuracy: 1.0000\n", "Epoch 124/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4058 - accuracy: 0.8594\n", "Epoch 124: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4695 - accuracy: 0.8449 - val_loss: 0.1323 - val_accuracy: 1.0000\n", "Epoch 125/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3853 - accuracy: 0.9062\n", "Epoch 125: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.4407 - accuracy: 0.8527 - val_loss: 0.1338 - val_accuracy: 1.0000\n", "Epoch 126/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4246 - accuracy: 0.8516\n", "Epoch 126: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.4442 - accuracy: 0.8382 - val_loss: 0.1327 - val_accuracy: 1.0000\n", "Epoch 127/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4424 - accuracy: 0.8438\n", "Epoch 127: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4383 - accuracy: 0.8538 - val_loss: 0.1293 - val_accuracy: 1.0000\n", "Epoch 128/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3875 - accuracy: 0.8438\n", "Epoch 128: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4188 - accuracy: 0.8527 - val_loss: 0.1246 - val_accuracy: 1.0000\n", "Epoch 129/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4059 - accuracy: 0.8594\n", "Epoch 129: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.4648 - accuracy: 0.8326 - val_loss: 0.1231 - val_accuracy: 1.0000\n", "Epoch 130/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5058 - accuracy: 0.7812\n", "Epoch 130: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.4675 - accuracy: 0.8326 - val_loss: 0.1246 - val_accuracy: 1.0000\n", "Epoch 131/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4472 - accuracy: 0.8594\n", "Epoch 131: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.4461 - accuracy: 0.8460 - val_loss: 0.1251 - val_accuracy: 1.0000\n", "Epoch 132/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3748 - accuracy: 0.8594\n", "Epoch 132: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 14ms/step - loss: 0.4340 - accuracy: 0.8404 - val_loss: 0.1239 - val_accuracy: 1.0000\n", "Epoch 133/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3641 - accuracy: 0.8828\n", "Epoch 133: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4062 - accuracy: 0.8560 - val_loss: 0.1225 - val_accuracy: 1.0000\n", "Epoch 134/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4655 - accuracy: 0.8516\n", "Epoch 134: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4171 - accuracy: 0.8482 - val_loss: 0.1201 - val_accuracy: 1.0000\n", "Epoch 135/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4534 - accuracy: 0.7891\n", "Epoch 135: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4622 - accuracy: 0.8214 - val_loss: 0.1193 - val_accuracy: 1.0000\n", "Epoch 136/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5003 - accuracy: 0.8203\n", "Epoch 136: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4203 - accuracy: 0.8516 - val_loss: 0.1158 - val_accuracy: 1.0000\n", "Epoch 137/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5115 - accuracy: 0.8125\n", "Epoch 137: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.4545 - accuracy: 0.8449 - val_loss: 0.1119 - val_accuracy: 1.0000\n", "Epoch 138/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3772 - accuracy: 0.8672\n", "Epoch 138: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4142 - accuracy: 0.8683 - val_loss: 0.1099 - val_accuracy: 1.0000\n", "Epoch 139/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4064 - accuracy: 0.8203\n", "Epoch 139: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.4176 - accuracy: 0.8404 - val_loss: 0.1098 - val_accuracy: 1.0000\n", "Epoch 140/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4584 - accuracy: 0.8281\n", "Epoch 140: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4118 - accuracy: 0.8661 - val_loss: 0.1098 - val_accuracy: 1.0000\n", "Epoch 141/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5142 - accuracy: 0.8438\n", "Epoch 141: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4255 - accuracy: 0.8516 - val_loss: 0.1088 - val_accuracy: 1.0000\n", "Epoch 142/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5238 - accuracy: 0.8516\n", "Epoch 142: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4262 - accuracy: 0.8516 - val_loss: 0.1090 - val_accuracy: 1.0000\n", "Epoch 143/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3943 - accuracy: 0.8516\n", "Epoch 143: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4456 - accuracy: 0.8482 - val_loss: 0.1092 - val_accuracy: 1.0000\n", "Epoch 144/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3600 - accuracy: 0.8906\n", "Epoch 144: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 13ms/step - loss: 0.4337 - accuracy: 0.8627 - val_loss: 0.1087 - val_accuracy: 1.0000\n", "Epoch 145/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3568 - accuracy: 0.8594\n", "Epoch 145: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4119 - accuracy: 0.8504 - val_loss: 0.1089 - val_accuracy: 1.0000\n", "Epoch 146/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3842 - accuracy: 0.8828\n", "Epoch 146: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4177 - accuracy: 0.8672 - val_loss: 0.1068 - val_accuracy: 1.0000\n", "Epoch 147/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4673 - accuracy: 0.8438\n", "Epoch 147: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.4278 - accuracy: 0.8560 - val_loss: 0.1066 - val_accuracy: 1.0000\n", "Epoch 148/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3590 - accuracy: 0.8516\n", "Epoch 148: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.4093 - accuracy: 0.8627 - val_loss: 0.1071 - val_accuracy: 0.9967\n", "Epoch 149/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3529 - accuracy: 0.8984\n", "Epoch 149: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3888 - accuracy: 0.8750 - val_loss: 0.1001 - val_accuracy: 1.0000\n", "Epoch 150/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4664 - accuracy: 0.8359\n", "Epoch 150: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.4132 - accuracy: 0.8650 - val_loss: 0.0956 - val_accuracy: 1.0000\n", "Epoch 151/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3959 - accuracy: 0.8438\n", "Epoch 151: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3549 - accuracy: 0.8828 - val_loss: 0.0939 - val_accuracy: 1.0000\n", "Epoch 152/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4567 - accuracy: 0.8125\n", "Epoch 152: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3868 - accuracy: 0.8661 - val_loss: 0.0969 - val_accuracy: 0.9967\n", "Epoch 153/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4143 - accuracy: 0.8672\n", "Epoch 153: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3859 - accuracy: 0.8728 - val_loss: 0.0964 - val_accuracy: 0.9967\n", "Epoch 154/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2979 - accuracy: 0.8828\n", "Epoch 154: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4170 - accuracy: 0.8560 - val_loss: 0.0934 - val_accuracy: 1.0000\n", "Epoch 155/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5908 - accuracy: 0.7734\n", "Epoch 155: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4196 - accuracy: 0.8493 - val_loss: 0.0923 - val_accuracy: 1.0000\n", "Epoch 156/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3710 - accuracy: 0.8672\n", "Epoch 156: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 13ms/step - loss: 0.4056 - accuracy: 0.8504 - val_loss: 0.0939 - val_accuracy: 1.0000\n", "Epoch 157/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4695 - accuracy: 0.8359\n", "Epoch 157: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3911 - accuracy: 0.8683 - val_loss: 0.0932 - val_accuracy: 1.0000\n", "Epoch 158/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3403 - accuracy: 0.8672\n", "Epoch 158: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4062 - accuracy: 0.8527 - val_loss: 0.0904 - val_accuracy: 1.0000\n", "Epoch 159/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3192 - accuracy: 0.8984\n", "Epoch 159: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.4130 - accuracy: 0.8571 - val_loss: 0.0882 - val_accuracy: 1.0000\n", "Epoch 160/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.6366 - accuracy: 0.8047\n", "Epoch 160: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 12ms/step - loss: 0.4163 - accuracy: 0.8583 - val_loss: 0.0904 - val_accuracy: 1.0000\n", "Epoch 161/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4883 - accuracy: 0.8438\n", "Epoch 161: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4265 - accuracy: 0.8583 - val_loss: 0.0931 - val_accuracy: 1.0000\n", "Epoch 162/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4310 - accuracy: 0.8516\n", "Epoch 162: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.4021 - accuracy: 0.8638 - val_loss: 0.0923 - val_accuracy: 1.0000\n", "Epoch 163/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3243 - accuracy: 0.8828\n", "Epoch 163: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3789 - accuracy: 0.8616 - val_loss: 0.0906 - val_accuracy: 1.0000\n", "Epoch 164/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4803 - accuracy: 0.8203\n", "Epoch 164: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3916 - accuracy: 0.8571 - val_loss: 0.0894 - val_accuracy: 1.0000\n", "Epoch 165/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3560 - accuracy: 0.8750\n", "Epoch 165: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4070 - accuracy: 0.8705 - val_loss: 0.0879 - val_accuracy: 1.0000\n", "Epoch 166/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4343 - accuracy: 0.8594\n", "Epoch 166: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3798 - accuracy: 0.8739 - val_loss: 0.0848 - val_accuracy: 1.0000\n", "Epoch 167/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2330 - accuracy: 0.9219\n", "Epoch 167: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4026 - accuracy: 0.8705 - val_loss: 0.0825 - val_accuracy: 1.0000\n", "Epoch 168/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4126 - accuracy: 0.8750\n", "Epoch 168: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3881 - accuracy: 0.8672 - val_loss: 0.0798 - val_accuracy: 1.0000\n", "Epoch 169/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4310 - accuracy: 0.8125\n", "Epoch 169: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3868 - accuracy: 0.8661 - val_loss: 0.0785 - val_accuracy: 1.0000\n", "Epoch 170/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4108 - accuracy: 0.8359\n", "Epoch 170: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3636 - accuracy: 0.8750 - val_loss: 0.0789 - val_accuracy: 1.0000\n", "Epoch 171/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4118 - accuracy: 0.8672\n", "Epoch 171: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3974 - accuracy: 0.8571 - val_loss: 0.0788 - val_accuracy: 1.0000\n", "Epoch 172/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3215 - accuracy: 0.9141\n", "Epoch 172: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3926 - accuracy: 0.8739 - val_loss: 0.0786 - val_accuracy: 1.0000\n", "Epoch 173/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3808 - accuracy: 0.8672\n", "Epoch 173: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3738 - accuracy: 0.8884 - val_loss: 0.0788 - val_accuracy: 1.0000\n", "Epoch 174/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3708 - accuracy: 0.8828\n", "Epoch 174: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.4060 - accuracy: 0.8638 - val_loss: 0.0802 - val_accuracy: 1.0000\n", "Epoch 175/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3536 - accuracy: 0.8828\n", "Epoch 175: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.4045 - accuracy: 0.8650 - val_loss: 0.0808 - val_accuracy: 1.0000\n", "Epoch 176/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3224 - accuracy: 0.8594\n", "Epoch 176: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3706 - accuracy: 0.8862 - val_loss: 0.0793 - val_accuracy: 1.0000\n", "Epoch 177/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3825 - accuracy: 0.8672\n", "Epoch 177: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3862 - accuracy: 0.8627 - val_loss: 0.0772 - val_accuracy: 1.0000\n", "Epoch 178/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3365 - accuracy: 0.9375\n", "Epoch 178: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3916 - accuracy: 0.8672 - val_loss: 0.0757 - val_accuracy: 1.0000\n", "Epoch 179/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2857 - accuracy: 0.8906\n", "Epoch 179: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3975 - accuracy: 0.8504 - val_loss: 0.0749 - val_accuracy: 1.0000\n", "Epoch 180/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.5019 - accuracy: 0.8750\n", "Epoch 180: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3776 - accuracy: 0.8728 - val_loss: 0.0733 - val_accuracy: 1.0000\n", "Epoch 181/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3274 - accuracy: 0.8828\n", "Epoch 181: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3573 - accuracy: 0.8795 - val_loss: 0.0713 - val_accuracy: 1.0000\n", "Epoch 182/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3428 - accuracy: 0.8672\n", "Epoch 182: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3950 - accuracy: 0.8683 - val_loss: 0.0715 - val_accuracy: 1.0000\n", "Epoch 183/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2811 - accuracy: 0.8984\n", "Epoch 183: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3688 - accuracy: 0.8728 - val_loss: 0.0717 - val_accuracy: 1.0000\n", "Epoch 184/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4300 - accuracy: 0.8594\n", "Epoch 184: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3896 - accuracy: 0.8717 - val_loss: 0.0729 - val_accuracy: 1.0000\n", "Epoch 185/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3787 - accuracy: 0.8594\n", "Epoch 185: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3690 - accuracy: 0.8627 - val_loss: 0.0735 - val_accuracy: 1.0000\n", "Epoch 186/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3147 - accuracy: 0.8906\n", "Epoch 186: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3684 - accuracy: 0.8783 - val_loss: 0.0721 - val_accuracy: 1.0000\n", "Epoch 187/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3502 - accuracy: 0.8750\n", "Epoch 187: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3894 - accuracy: 0.8661 - val_loss: 0.0712 - val_accuracy: 1.0000\n", "Epoch 188/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4659 - accuracy: 0.8594\n", "Epoch 188: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3817 - accuracy: 0.8683 - val_loss: 0.0709 - val_accuracy: 1.0000\n", "Epoch 189/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3508 - accuracy: 0.8516\n", "Epoch 189: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.3744 - accuracy: 0.8862 - val_loss: 0.0708 - val_accuracy: 1.0000\n", "Epoch 190/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4333 - accuracy: 0.8281\n", "Epoch 190: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3481 - accuracy: 0.8728 - val_loss: 0.0707 - val_accuracy: 1.0000\n", "Epoch 191/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3793 - accuracy: 0.8516\n", "Epoch 191: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3693 - accuracy: 0.8616 - val_loss: 0.0695 - val_accuracy: 1.0000\n", "Epoch 192/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4561 - accuracy: 0.8672\n", "Epoch 192: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3898 - accuracy: 0.8694 - val_loss: 0.0696 - val_accuracy: 1.0000\n", "Epoch 193/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3225 - accuracy: 0.8828\n", "Epoch 193: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3574 - accuracy: 0.8806 - val_loss: 0.0696 - val_accuracy: 1.0000\n", "Epoch 194/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3772 - accuracy: 0.8516\n", "Epoch 194: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3636 - accuracy: 0.8884 - val_loss: 0.0687 - val_accuracy: 1.0000\n", "Epoch 195/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2985 - accuracy: 0.8828\n", "Epoch 195: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3598 - accuracy: 0.8795 - val_loss: 0.0668 - val_accuracy: 1.0000\n", "Epoch 196/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3587 - accuracy: 0.8750\n", "Epoch 196: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3751 - accuracy: 0.8650 - val_loss: 0.0655 - val_accuracy: 1.0000\n", "Epoch 197/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3522 - accuracy: 0.8984\n", "Epoch 197: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.4063 - accuracy: 0.8650 - val_loss: 0.0642 - val_accuracy: 1.0000\n", "Epoch 198/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3588 - accuracy: 0.8672\n", "Epoch 198: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3526 - accuracy: 0.8705 - val_loss: 0.0636 - val_accuracy: 1.0000\n", "Epoch 199/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2928 - accuracy: 0.8906\n", "Epoch 199: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3655 - accuracy: 0.8862 - val_loss: 0.0640 - val_accuracy: 1.0000\n", "Epoch 200/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3102 - accuracy: 0.8828\n", "Epoch 200: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.4039 - accuracy: 0.8761 - val_loss: 0.0656 - val_accuracy: 1.0000\n", "Epoch 201/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4681 - accuracy: 0.8594\n", "Epoch 201: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3485 - accuracy: 0.8772 - val_loss: 0.0653 - val_accuracy: 1.0000\n", "Epoch 202/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3640 - accuracy: 0.8516\n", "Epoch 202: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3664 - accuracy: 0.8817 - val_loss: 0.0653 - val_accuracy: 1.0000\n", "Epoch 203/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3235 - accuracy: 0.8672\n", "Epoch 203: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3625 - accuracy: 0.8717 - val_loss: 0.0662 - val_accuracy: 1.0000\n", "Epoch 204/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2736 - accuracy: 0.9141\n", "Epoch 204: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3617 - accuracy: 0.8661 - val_loss: 0.0683 - val_accuracy: 1.0000\n", "Epoch 205/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3911 - accuracy: 0.8516\n", "Epoch 205: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3764 - accuracy: 0.8683 - val_loss: 0.0683 - val_accuracy: 1.0000\n", "Epoch 206/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3427 - accuracy: 0.8906\n", "Epoch 206: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3898 - accuracy: 0.8638 - val_loss: 0.0655 - val_accuracy: 1.0000\n", "Epoch 207/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3679 - accuracy: 0.8438\n", "Epoch 207: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3729 - accuracy: 0.8672 - val_loss: 0.0630 - val_accuracy: 1.0000\n", "Epoch 208/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3359 - accuracy: 0.8984\n", "Epoch 208: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.3867 - accuracy: 0.8728 - val_loss: 0.0613 - val_accuracy: 1.0000\n", "Epoch 209/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3829 - accuracy: 0.8828\n", "Epoch 209: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3573 - accuracy: 0.8750 - val_loss: 0.0609 - val_accuracy: 1.0000\n", "Epoch 210/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3709 - accuracy: 0.8984\n", "Epoch 210: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3852 - accuracy: 0.8661 - val_loss: 0.0613 - val_accuracy: 1.0000\n", "Epoch 211/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4407 - accuracy: 0.8203\n", "Epoch 211: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3781 - accuracy: 0.8661 - val_loss: 0.0609 - val_accuracy: 1.0000\n", "Epoch 212/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3696 - accuracy: 0.8672\n", "Epoch 212: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3486 - accuracy: 0.8717 - val_loss: 0.0600 - val_accuracy: 1.0000\n", "Epoch 213/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2993 - accuracy: 0.8906\n", "Epoch 213: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3797 - accuracy: 0.8806 - val_loss: 0.0592 - val_accuracy: 1.0000\n", "Epoch 214/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2994 - accuracy: 0.9375\n", "Epoch 214: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3523 - accuracy: 0.8850 - val_loss: 0.0577 - val_accuracy: 1.0000\n", "Epoch 215/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3007 - accuracy: 0.8984\n", "Epoch 215: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3617 - accuracy: 0.8772 - val_loss: 0.0567 - val_accuracy: 1.0000\n", "Epoch 216/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3746 - accuracy: 0.8594\n", "Epoch 216: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3443 - accuracy: 0.8817 - val_loss: 0.0562 - val_accuracy: 1.0000\n", "Epoch 217/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4334 - accuracy: 0.8906\n", "Epoch 217: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3772 - accuracy: 0.8728 - val_loss: 0.0571 - val_accuracy: 1.0000\n", "Epoch 218/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3183 - accuracy: 0.8828\n", "Epoch 218: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3672 - accuracy: 0.8672 - val_loss: 0.0593 - val_accuracy: 1.0000\n", "Epoch 219/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3043 - accuracy: 0.8984\n", "Epoch 219: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3525 - accuracy: 0.8772 - val_loss: 0.0603 - val_accuracy: 1.0000\n", "Epoch 220/1000\n", "3/7 [===========>..................] - ETA: 0s - loss: 0.3467 - accuracy: 0.8854\n", "Epoch 220: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 18ms/step - loss: 0.3641 - accuracy: 0.8783 - val_loss: 0.0598 - val_accuracy: 1.0000\n", "Epoch 221/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3027 - accuracy: 0.9219\n", "Epoch 221: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3764 - accuracy: 0.8906 - val_loss: 0.0604 - val_accuracy: 1.0000\n", "Epoch 222/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3319 - accuracy: 0.8984\n", "Epoch 222: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3632 - accuracy: 0.8772 - val_loss: 0.0593 - val_accuracy: 1.0000\n", "Epoch 223/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3942 - accuracy: 0.8359\n", "Epoch 223: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3786 - accuracy: 0.8616 - val_loss: 0.0580 - val_accuracy: 1.0000\n", "Epoch 224/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2880 - accuracy: 0.8906\n", "Epoch 224: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3398 - accuracy: 0.8817 - val_loss: 0.0594 - val_accuracy: 1.0000\n", "Epoch 225/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3165 - accuracy: 0.8672\n", "Epoch 225: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3307 - accuracy: 0.8783 - val_loss: 0.0602 - val_accuracy: 0.9967\n", "Epoch 226/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4289 - accuracy: 0.8516\n", "Epoch 226: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3693 - accuracy: 0.8728 - val_loss: 0.0587 - val_accuracy: 0.9967\n", "Epoch 227/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3906 - accuracy: 0.8281\n", "Epoch 227: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3790 - accuracy: 0.8638 - val_loss: 0.0581 - val_accuracy: 0.9967\n", "Epoch 228/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4300 - accuracy: 0.8438\n", "Epoch 228: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3434 - accuracy: 0.8862 - val_loss: 0.0574 - val_accuracy: 0.9967\n", "Epoch 229/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2357 - accuracy: 0.9531\n", "Epoch 229: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.3354 - accuracy: 0.8940 - val_loss: 0.0559 - val_accuracy: 1.0000\n", "Epoch 230/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3776 - accuracy: 0.8750\n", "Epoch 230: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3523 - accuracy: 0.8717 - val_loss: 0.0548 - val_accuracy: 1.0000\n", "Epoch 231/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3596 - accuracy: 0.8750\n", "Epoch 231: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3582 - accuracy: 0.8705 - val_loss: 0.0559 - val_accuracy: 1.0000\n", "Epoch 232/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3542 - accuracy: 0.8672\n", "Epoch 232: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3976 - accuracy: 0.8661 - val_loss: 0.0572 - val_accuracy: 1.0000\n", "Epoch 233/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3792 - accuracy: 0.8750\n", "Epoch 233: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3744 - accuracy: 0.8728 - val_loss: 0.0575 - val_accuracy: 1.0000\n", "Epoch 234/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4158 - accuracy: 0.8750\n", "Epoch 234: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3663 - accuracy: 0.8839 - val_loss: 0.0594 - val_accuracy: 0.9967\n", "Epoch 235/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2779 - accuracy: 0.9141\n", "Epoch 235: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3293 - accuracy: 0.8917 - val_loss: 0.0596 - val_accuracy: 0.9967\n", "Epoch 236/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4061 - accuracy: 0.8438\n", "Epoch 236: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3290 - accuracy: 0.8839 - val_loss: 0.0586 - val_accuracy: 0.9967\n", "Epoch 237/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2940 - accuracy: 0.9141\n", "Epoch 237: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.3237 - accuracy: 0.8839 - val_loss: 0.0547 - val_accuracy: 0.9967\n", "Epoch 238/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3744 - accuracy: 0.8750\n", "Epoch 238: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3482 - accuracy: 0.8839 - val_loss: 0.0515 - val_accuracy: 1.0000\n", "Epoch 239/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3540 - accuracy: 0.8438\n", "Epoch 239: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3414 - accuracy: 0.8806 - val_loss: 0.0499 - val_accuracy: 1.0000\n", "Epoch 240/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4063 - accuracy: 0.8906\n", "Epoch 240: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3038 - accuracy: 0.9018 - val_loss: 0.0495 - val_accuracy: 0.9967\n", "Epoch 241/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3589 - accuracy: 0.8438\n", "Epoch 241: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3200 - accuracy: 0.8772 - val_loss: 0.0478 - val_accuracy: 1.0000\n", "Epoch 242/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3359 - accuracy: 0.8672\n", "Epoch 242: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3363 - accuracy: 0.8828 - val_loss: 0.0487 - val_accuracy: 0.9967\n", "Epoch 243/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3515 - accuracy: 0.8828\n", "Epoch 243: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3577 - accuracy: 0.8739 - val_loss: 0.0490 - val_accuracy: 1.0000\n", "Epoch 244/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3512 - accuracy: 0.8516\n", "Epoch 244: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3375 - accuracy: 0.8884 - val_loss: 0.0505 - val_accuracy: 0.9967\n", "Epoch 245/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3358 - accuracy: 0.8828\n", "Epoch 245: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3300 - accuracy: 0.8984 - val_loss: 0.0530 - val_accuracy: 0.9967\n", "Epoch 246/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3527 - accuracy: 0.8672\n", "Epoch 246: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.3620 - accuracy: 0.8694 - val_loss: 0.0561 - val_accuracy: 0.9967\n", "Epoch 247/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4223 - accuracy: 0.8438\n", "Epoch 247: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3317 - accuracy: 0.8761 - val_loss: 0.0577 - val_accuracy: 0.9967\n", "Epoch 248/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3480 - accuracy: 0.8672\n", "Epoch 248: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3211 - accuracy: 0.8828 - val_loss: 0.0579 - val_accuracy: 0.9967\n", "Epoch 249/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3292 - accuracy: 0.8984\n", "Epoch 249: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3494 - accuracy: 0.8783 - val_loss: 0.0576 - val_accuracy: 0.9967\n", "Epoch 250/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3777 - accuracy: 0.8906\n", "Epoch 250: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3512 - accuracy: 0.8850 - val_loss: 0.0566 - val_accuracy: 0.9967\n", "Epoch 251/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4060 - accuracy: 0.8828\n", "Epoch 251: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3180 - accuracy: 0.9007 - val_loss: 0.0549 - val_accuracy: 0.9967\n", "Epoch 252/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2825 - accuracy: 0.9219\n", "Epoch 252: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3584 - accuracy: 0.8750 - val_loss: 0.0514 - val_accuracy: 0.9967\n", "Epoch 253/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3215 - accuracy: 0.8750\n", "Epoch 253: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3641 - accuracy: 0.8661 - val_loss: 0.0502 - val_accuracy: 0.9967\n", "Epoch 254/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3451 - accuracy: 0.8672\n", "Epoch 254: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3505 - accuracy: 0.8850 - val_loss: 0.0475 - val_accuracy: 0.9967\n", "Epoch 255/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2782 - accuracy: 0.9062\n", "Epoch 255: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3310 - accuracy: 0.8873 - val_loss: 0.0469 - val_accuracy: 0.9967\n", "Epoch 256/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2787 - accuracy: 0.8906\n", "Epoch 256: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3669 - accuracy: 0.8705 - val_loss: 0.0479 - val_accuracy: 1.0000\n", "Epoch 257/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4374 - accuracy: 0.8203\n", "Epoch 257: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3240 - accuracy: 0.8884 - val_loss: 0.0484 - val_accuracy: 1.0000\n", "Epoch 258/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3439 - accuracy: 0.8906\n", "Epoch 258: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3462 - accuracy: 0.8873 - val_loss: 0.0477 - val_accuracy: 1.0000\n", "Epoch 259/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2659 - accuracy: 0.9297\n", "Epoch 259: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 14ms/step - loss: 0.3225 - accuracy: 0.8962 - val_loss: 0.0470 - val_accuracy: 1.0000\n", "Epoch 260/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3638 - accuracy: 0.8594\n", "Epoch 260: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3185 - accuracy: 0.8895 - val_loss: 0.0471 - val_accuracy: 0.9967\n", "Epoch 261/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3246 - accuracy: 0.8906\n", "Epoch 261: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.2960 - accuracy: 0.9029 - val_loss: 0.0454 - val_accuracy: 0.9967\n", "Epoch 262/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3091 - accuracy: 0.8906\n", "Epoch 262: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3253 - accuracy: 0.8929 - val_loss: 0.0431 - val_accuracy: 1.0000\n", "Epoch 263/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3367 - accuracy: 0.8906\n", "Epoch 263: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3431 - accuracy: 0.8850 - val_loss: 0.0421 - val_accuracy: 1.0000\n", "Epoch 264/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3044 - accuracy: 0.8984\n", "Epoch 264: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3177 - accuracy: 0.8862 - val_loss: 0.0405 - val_accuracy: 1.0000\n", "Epoch 265/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4174 - accuracy: 0.8828\n", "Epoch 265: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3335 - accuracy: 0.8884 - val_loss: 0.0407 - val_accuracy: 1.0000\n", "Epoch 266/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3660 - accuracy: 0.9062\n", "Epoch 266: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3563 - accuracy: 0.8683 - val_loss: 0.0428 - val_accuracy: 1.0000\n", "Epoch 267/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2707 - accuracy: 0.9219\n", "Epoch 267: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3411 - accuracy: 0.8906 - val_loss: 0.0434 - val_accuracy: 1.0000\n", "Epoch 268/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2434 - accuracy: 0.9219\n", "Epoch 268: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 10ms/step - loss: 0.3090 - accuracy: 0.8984 - val_loss: 0.0437 - val_accuracy: 0.9967\n", "Epoch 269/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3037 - accuracy: 0.8828\n", "Epoch 269: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3325 - accuracy: 0.8895 - val_loss: 0.0435 - val_accuracy: 0.9967\n", "Epoch 270/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3515 - accuracy: 0.8516\n", "Epoch 270: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3327 - accuracy: 0.8795 - val_loss: 0.0432 - val_accuracy: 0.9967\n", "Epoch 271/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4099 - accuracy: 0.8203\n", "Epoch 271: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3368 - accuracy: 0.8750 - val_loss: 0.0436 - val_accuracy: 0.9967\n", "Epoch 272/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2855 - accuracy: 0.8906\n", "Epoch 272: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3284 - accuracy: 0.8951 - val_loss: 0.0434 - val_accuracy: 1.0000\n", "Epoch 273/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4240 - accuracy: 0.8672\n", "Epoch 273: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3148 - accuracy: 0.8828 - val_loss: 0.0430 - val_accuracy: 1.0000\n", "Epoch 274/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4243 - accuracy: 0.8359\n", "Epoch 274: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 7ms/step - loss: 0.3725 - accuracy: 0.8560 - val_loss: 0.0434 - val_accuracy: 1.0000\n", "Epoch 275/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2785 - accuracy: 0.9062\n", "Epoch 275: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3457 - accuracy: 0.8906 - val_loss: 0.0438 - val_accuracy: 1.0000\n", "Epoch 276/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2732 - accuracy: 0.8984\n", "Epoch 276: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3357 - accuracy: 0.8828 - val_loss: 0.0436 - val_accuracy: 1.0000\n", "Epoch 277/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2740 - accuracy: 0.9141\n", "Epoch 277: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3240 - accuracy: 0.8850 - val_loss: 0.0446 - val_accuracy: 1.0000\n", "Epoch 278/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3453 - accuracy: 0.8906\n", "Epoch 278: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3296 - accuracy: 0.8951 - val_loss: 0.0471 - val_accuracy: 0.9967\n", "Epoch 279/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.4235 - accuracy: 0.8438\n", "Epoch 279: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3366 - accuracy: 0.8873 - val_loss: 0.0482 - val_accuracy: 0.9967\n", "Epoch 280/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3100 - accuracy: 0.9062\n", "Epoch 280: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 14ms/step - loss: 0.3184 - accuracy: 0.8973 - val_loss: 0.0490 - val_accuracy: 0.9967\n", "Epoch 281/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.3846 - accuracy: 0.8594\n", "Epoch 281: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3386 - accuracy: 0.8940 - val_loss: 0.0500 - val_accuracy: 0.9967\n", "Epoch 282/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2645 - accuracy: 0.8906\n", "Epoch 282: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3211 - accuracy: 0.8884 - val_loss: 0.0487 - val_accuracy: 0.9967\n", "Epoch 283/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2486 - accuracy: 0.9297\n", "Epoch 283: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 9ms/step - loss: 0.3113 - accuracy: 0.8895 - val_loss: 0.0465 - val_accuracy: 0.9967\n", "Epoch 284/1000\n", "1/7 [===>..........................] - ETA: 0s - loss: 0.2351 - accuracy: 0.9219\n", "Epoch 284: saving model to model/keypoint_classifier\\keypoint_classifier.hdf5\n", "7/7 [==============================] - 0s 8ms/step - loss: 0.3077 - accuracy: 0.9007 - val_loss: 0.0451 - val_accuracy: 0.9967\n", "Epoch 284: early stopping\n"]}, {"data": {"text/plain": ["<keras.callbacks.History at 0x1c78840f748>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["model.fit(\n", "    X_train,\n", "    y_train,\n", "    epochs=1000,\n", "    batch_size=128,\n", "    validation_data=(X_test, y_test),\n", "    callbacks=[cp_callback, es_callback]\n", ")"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "pxvb2Y299hE3", "outputId": "59eb3185-2e37-4b9e-bc9d-ab1b8ac29b7f"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3/3 [==============================] - 0s 0s/step - loss: 0.0451 - accuracy: 0.9967\n"]}], "source": ["# Model evaluation\n", "val_loss, val_acc = model.evaluate(X_test, y_test, batch_size=128)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"id": "RBkmDeUW9hE4"}, "outputs": [], "source": ["# Loading the saved model\n", "model = tf.keras.models.load_model(model_save_path)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "tFz9Tb0I9hE4", "outputId": "1c3b3528-54ae-4ee2-ab04-77429211cbef"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1/1 [==============================] - 0s 87ms/step\n", "[3.1791881e-06 6.1401374e-06 5.2847351e-08 9.9998975e-01 1.4648843e-07\n", " 7.4153553e-07]\n", "3\n"]}], "source": ["# Inference test\n", "predict_result = model.predict(np.array([X_test[0]]))\n", "print(np.squeeze(predict_result))\n", "print(np.argmax(np.squeeze(predict_result)))"]}, {"cell_type": "markdown", "metadata": {"id": "S3U4yNWx9hE4"}, "source": ["# Confusion matrix"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 582}, "id": "AP1V6SCk9hE5", "outputId": "08e41a80-7a4a-4619-8125-ecc371368d19"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10/10 [==============================] - 0s 2ms/step\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 700x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Classification Report\n", "              precision    recall  f1-score   support\n", "\n", "           0       1.00      1.00      1.00        48\n", "           1       1.00      1.00      1.00        51\n", "           2       1.00      1.00      1.00        58\n", "           3       1.00      1.00      1.00        47\n", "           4       1.00      0.98      0.99        59\n", "           5       0.97      1.00      0.99        36\n", "\n", "    accuracy                           1.00       299\n", "   macro avg       1.00      1.00      1.00       299\n", "weighted avg       1.00      1.00      1.00       299\n", "\n"]}], "source": ["import pandas as pd\n", "import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "from sklearn.metrics import confusion_matrix, classification_report\n", "\n", "def print_confusion_matrix(y_true, y_pred, report=True):\n", "    labels = sorted(list(set(y_true)))\n", "    cmx_data = confusion_matrix(y_true, y_pred, labels=labels)\n", "    \n", "    df_cmx = pd.DataFrame(cmx_data, index=labels, columns=labels)\n", " \n", "    fig, ax = plt.subplots(figsize=(7, 6))\n", "    sns.heatmap(df_cmx, annot=True, fmt='g' ,square=False)\n", "    ax.set_ylim(len(set(y_true)), 0)\n", "    plt.show()\n", "    \n", "    if report:\n", "        print('Classification Report')\n", "        print(classification_report(y_test, y_pred))\n", "\n", "Y_pred = model.predict(X_test)\n", "y_pred = np.argmax(Y_pred, axis=1)\n", "\n", "print_confusion_matrix(y_test, y_pred)"]}, {"cell_type": "markdown", "metadata": {"id": "FNP6aqzc9hE5"}, "source": ["# Convert to model for Tensorflow-Lite"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"id": "ODjnYyld9hE6"}, "outputs": [], "source": ["# Save as a model dedicated to inference\n", "model.save(model_save_path, include_optimizer=False)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "zRfuK8Y59hE6", "outputId": "a4ca585c-b5d5-4244-8291-8674063209bb"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:absl:Found untraced functions such as _update_step_xla while saving (showing 1 of 1). These functions will not be directly callable after loading.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["INFO:tensorflow:Assets written to: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpcci639y0\\assets\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:tensorflow:Assets written to: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpcci639y0\\assets\n"]}, {"data": {"text/plain": ["6632"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# Transform model (quantization)\n", "\n", "converter = tf.lite.TFLiteConverter.from_keras_model(model)\n", "converter.optimizations = [tf.lite.Optimize.DEFAULT]\n", "tflite_quantized_model = converter.convert()\n", "\n", "open(tflite_save_path, 'wb').write(tflite_quantized_model)"]}, {"cell_type": "markdown", "metadata": {"id": "CHBPBXdx9hE6"}, "source": ["# Inference test"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"id": "mGAzLocO9hE7"}, "outputs": [], "source": ["interpreter = tf.lite.Interpreter(model_path=tflite_save_path)\n", "interpreter.allocate_tensors()"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"id": "oQuDK8YS9hE7"}, "outputs": [], "source": ["# Get I / O tensor\n", "input_details = interpreter.get_input_details()\n", "output_details = interpreter.get_output_details()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {"id": "2_ixAf_l9hE7"}, "outputs": [], "source": ["interpreter.set_tensor(input_details[0]['index'], np.array([X_test[0]]))"]}, {"cell_type": "code", "execution_count": 21, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "s4FoAnuc9hE7", "outputId": "91f18257-8d8b-4ef3-c558-e9b5f94fabbf", "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Wall time: 0 ns\n"]}], "source": ["%%time\n", "# Inference implementation\n", "interpreter.invoke()\n", "tflite_results = interpreter.get_tensor(output_details[0]['index'])"]}, {"cell_type": "code", "execution_count": 22, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "vONjp19J9hE8", "outputId": "77205e24-fd00-42c4-f7b6-e06e527c2cba"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[3.1791876e-06 6.1401483e-06 5.2847351e-08 9.9998975e-01 1.4648843e-07\n", " 7.4153627e-07]\n", "3\n"]}], "source": ["print(np.squeeze(tflite_results))\n", "print(np.argmax(np.squeeze(tflite_results)))"]}], "metadata": {"accelerator": "GPU", "colab": {"collapsed_sections": [], "name": "keypoint_classification_EN.ipynb", "provenance": [], "toc_visible": true}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.13"}}, "nbformat": 4, "nbformat_minor": 0}