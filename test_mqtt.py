from paho.mqtt import client as mqtt_client
from paho.mqtt.client import CallbackAPIVersion # Correct import

import random
import time

# --- Your existing variables ---
broker = '192.168.203.7'
port = 1883
topic = 'nilesh'
client_id = f'subscribe-{random.randint(0, 100)}'
username = 's'
password = '12345678'
# --- End of existing variables ---

def connect_mqtt():
    # Define the callback using the newer signature
    def on_connect(client, userdata, flags, reason_code, properties):
        if reason_code == 0:
            print("Connected to MQTT Broker!")
            print(f"  Session Present: {flags.session_present}")
            if properties:
                 print("  Connection Properties:")
                 # Handle properties as a dictionary or Properties object
                 props_items = properties.items() if isinstance(properties, dict) else vars(properties).items()
                 for key, value in props_items:
                     if not key.startswith('_') and value is not None: # Filter internal/None
                         print(f"    {key}: {value}")
        else:
            print(f"Failed to connect, return code {reason_code}\n")

    # --- CORRECTED CLIENT CREATION ---
    # Pass callback_api_version FIRST, then use keyword for client_id
    client = mqtt_client.Client(CallbackAPIVersion.VERSION2, client_id=client_id)
    # --- END CORRECTION ---

    client.username_pw_set(username, password)
    client.on_connect = on_connect  # Assign the defined callback

    # It's generally better to connect *after* assigning callbacks
    # and before starting the loop, but connecting here works too.
    client.connect(broker, port)
    return client

# Example usage (you would call this from your main script)
if __name__ == "__main__":
    print("Attempting to connect...")
    mqtt_client_instance = None # Initialize to None
    try:
        mqtt_client_instance = connect_mqtt()
        mqtt_client_instance.loop_start()
        print("MQTT Client running. Press Ctrl+C to exit.")
        while True:
            time.sleep(1)
    except mqtt_client.WebsocketConnectionError as e:
         print(f"Websocket connection error: {e}")
    except ConnectionRefusedError as e:
         print(f"Connection refused. Is the broker running at {broker}:{port}? Error: {e}")
    except OSError as e:
         print(f"Network error (e.g., host unreachable): {e}")
    except Exception as e:
         print(f"An unexpected error occurred: {e}")
         # Print detailed traceback for unexpected errors
         import traceback
         traceback.print_exc()
    except KeyboardInterrupt:
        print("\nDisconnecting...")
    finally:
        # Ensure cleanup happens even if connection failed partially
        if mqtt_client_instance and mqtt_client_instance.is_connected():
            mqtt_client_instance.loop_stop()
            mqtt_client_instance.disconnect()
            print("Disconnected.")
        elif mqtt_client_instance:
             # If loop wasn't started or connection failed before loop
             try:
                 mqtt_client_instance.disconnect() # Attempt disconnect anyway
             except Exception:
                 pass # Ignore errors during cleanup if already problematic
             print("Cleanup attempted.")
        else:
            print("No client instance to clean up.")